/* 全局变量 */
:root {
  --primary-color: #7928CA;
  --primary-light: #9b4dca;
  --primary-lightest: #f5f0ff;
  --gradient-primary: linear-gradient(135deg, #7928CA, #9b4dca);
  --gradient-secondary: linear-gradient(135deg, #f5f0ff, #ffffff);
  --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-muted: #999999;
  --text-light: #cccccc;
  --background-color: #f8f8f8;
  --card-background: #ffffff;
  --border-color: rgba(232, 232, 232, 0.8);
  --shadow-color: rgba(0, 0, 0, 0.05);
  --input-area-height: 120rpx;
  --safe-bottom: env(safe-area-inset-bottom);
}

/* 页面容器 */
.page-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
}

/* 聊天容器 */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-bottom: calc(env(safe-area-inset-bottom) + 140rpx); /* 调整为140rpx以适配tabbar */
}

/* 背景装饰 */
.chat-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60%;
  background: linear-gradient(180deg, rgba(121, 40, 202, 0.03) 0%, transparent 100%);
  pointer-events: none;
  z-index: 0;
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 20rpx;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 200rpx; /* 增加底部内边距 */
}

/* 底部安全区域 */
.safe-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: calc(env(safe-area-inset-bottom) + 140rpx); /* 调整高度 */
  background: transparent;
  pointer-events: none;
}

/* 悬浮输入区域 - 美化和修复动画 */
.fab-input-container {
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom) + 160rpx); /* 调整为160rpx */
  right: 30rpx;
  z-index: 999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fab-input-container.expanded {
  left: 30rpx;
  right: 30rpx;
  bottom: calc(env(safe-area-inset-bottom) + 160rpx); /* 保持一致 */
}

.fab-input-container.collapsed {
  width: auto;
  height: auto;
}

/* 悬浮球 */
.fab-ball {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #9575cd 0%, #7e57c2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(149, 117, 205, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 8rpx 24rpx rgba(149, 117, 205, 0.4);
  }
  50% {
    box-shadow: 0 8rpx 30rpx rgba(149, 117, 205, 0.6);
  }
  100% {
    box-shadow: 0 8rpx 24rpx rgba(149, 117, 205, 0.4);
  }
}

.fab-ball:active {
  transform: scale(0.95);
  animation: none;
}

.fab-icon {
  font-size: 50rpx;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 展开后的输入区域 */
.expanded-input-area {
  background: white;
  border-radius: 25rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(149, 117, 205, 0.2);
  display: flex;
  align-items: flex-end;
  gap: 20rpx;
  animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 欢迎区域 */
.welcome-section {
  margin-bottom: 50rpx;
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.welcome-card {
  background: var(--gradient-card);
  border-radius: 28rpx;
  padding: 48rpx;
  text-align: center;
  box-shadow: 0 12rpx 40rpx var(--shadow-color);
  margin-bottom: 36rpx;
  border: 1rpx solid rgba(232, 229, 255, 0.5);
  position: relative;
  overflow: hidden;
}

.welcome-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(121, 40, 202, 0.05) 0%, transparent 70%);
  animation: pulse 4s ease-in-out infinite;
}

.welcome-avatar {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: inline-block;
  animation: gentle-float 3s ease-in-out infinite;
}

.welcome-title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.welcome-desc {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 快捷操作 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin: 24rpx 0;
}

.quick-item {
  background: var(--card-background);
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 6rpx 20rpx var(--shadow-color);
  transition: all 0.3s ease;
}

.quick-item:active {
  transform: scale(0.98);
  background: var(--primary-lightest);
}

.quick-icon {
  font-size: 36rpx;
}

.quick-text {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 消息列表 */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 240rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 消息项样式 */
.message-item {
  margin-bottom: 36rpx;
  animation: message-slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  box-sizing: border-box;
  padding: 0 12rpx;
}

/* 消息时间包装器 */
.message-time-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 12rpx;
  width: 100%;
}

/* 消息时间样式 */
.message-time {
  font-size: 24rpx;
  color: var(--text-muted);
  background-color: rgba(0, 0, 0, 0.04);
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  display: inline-block;
  line-height: 1.4;
}

/* 用户消息 */
.message-user {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 16rpx;
  max-width: 100%;
}

.message-user .message-content {
  background: var(--gradient-primary);
  color: white;
  padding: 24rpx 28rpx;
  border-radius: 24rpx 24rpx 8rpx 24rpx;
  max-width: 70%;
  margin-right: 0;
  box-shadow: 0 8rpx 24rpx rgba(121, 40, 202, 0.25);
  position: relative;
  overflow: hidden;
  word-break: break-all;
}

.message-user .message-avatar {
  width: 80rpx;
  height: 80rpx;
  background: var(--gradient-secondary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  flex-shrink: 0;
}

/* AI消息 */
.message-ai {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 16rpx;
  max-width: 100%;
}

.message-ai .message-avatar {
  width: 80rpx;
  height: 80rpx;
  background: var(--gradient-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  flex-shrink: 0;
  box-shadow: 0 6rpx 20rpx rgba(121, 40, 202, 0.3);
  animation: avatar-glow 3s ease-in-out infinite;
}

.message-ai .message-content {
  background: var(--card-background);
  padding: 24rpx 28rpx;
  border-radius: 24rpx 24rpx 24rpx 8rpx;
  max-width: 70%;
  box-shadow: 0 6rpx 20rpx var(--shadow-color);
  border: 1rpx solid var(--border-color);
  position: relative;
  word-break: break-all;
}

/* 消息文本 */
.message-text {
  font-size: 30rpx;
  line-height: 1.7;
  color: var(--text-primary);
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message-user .message-text {
  color: white;
}

/* 打字机效果的文本样式 */
.message-text.typing-effect {
  position: relative;
}

.message-text.typing-effect::after {
  content: '|';
  position: absolute;
  color: var(--primary-color);
  animation: cursor-blink 1s step-end infinite;
}

@keyframes cursor-blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.message-actions {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid var(--border-color);
}

.action-btn {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 18rpx;
  padding: 18rpx 36rpx;
  font-size: 26rpx;
  line-height: 1;
  box-shadow: 0 6rpx 16rpx rgba(121, 40, 202, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 3rpx 8rpx rgba(121, 40, 202, 0.25);
}

/* 输入中状态 */
.typing-indicator {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
  animation: typing-fade-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.typing-avatar {
  width: 72rpx;
  height: 72rpx;
  background: var(--gradient-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(121, 40, 202, 0.3);
}

.typing-content {
  background: var(--card-background);
  padding: 24rpx 32rpx;
  border-radius: 24rpx 24rpx 24rpx 8rpx;
  box-shadow: 0 6rpx 20rpx var(--shadow-color);
  border: 1rpx solid var(--border-color);
  display: flex;
  align-items: center;
}

.typing-dots {
  display: flex;
  gap: 10rpx;
  margin-right: 20rpx;
}

.typing-dots .dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: var(--primary-color);
  animation: typing-dot 1.5s infinite ease-in-out;
}

.typing-dots .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots .dot:nth-child(3) {
  animation-delay: 0.4s;
}

.typing-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-style: italic;
}

/* 优化动画效果 */
@keyframes message-slide-in {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing-fade-in {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes typing-dot {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes gentle-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8rpx);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 