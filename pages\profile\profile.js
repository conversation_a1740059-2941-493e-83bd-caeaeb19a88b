// pages/profile/profile.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    points: 0,
    level: 1,
    favorites: [],
    records: [],
    settings: {
      theme: 'light',
      notification: true
    },
    activeTab: 'records'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
    this.loadUserData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    try {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 2
        })
      }
    } catch (error) {
      console.error('设置 TabBar 选中状态失败:', error)
    }
    this.loadUserData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (this.data.userInfo.userId) {
      this.loadUserStats()
    }
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '玄学社区',
      path: '/pages/profile/profile'
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({ userInfo })
    }
  },

  // 登录
  login() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        const userInfo = {
          ...res.userInfo,
          userId: 'user_' + Date.now().toString().slice(-6),
          loginTime: new Date().toLocaleString()
        }
        
        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', userInfo)
        
        // 更新页面数据
        this.setData({ userInfo })
        
        // 加载用户统计数据
        this.loadUserStats()
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
      },
      fail: (err) => {
        console.error('登录失败:', err)
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        })
      }
    })
  },

  // 加载用户统计数据
  loadUserStats() {
    // 模拟数据，实际项目中应该从服务器获取
    const mockStats = {
      posts: 12,
      followers: 128,
      following: 56
    }
    
    this.setData({
      userStats: mockStats
    })
  },

  // 页面导航
  navigateTo(e) {
    const url = e.currentTarget.dataset.url
    if (!this.data.userInfo.userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }
    wx.navigateTo({ url })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的用户信息
          wx.removeStorageSync('userInfo')
          // 重置页面数据
          this.setData({
            userInfo: {},
            userStats: {
              posts: 0,
              followers: 0,
              following: 0
            }
          })
          // 返回首页
          wx.switchTab({
            url: '/pages/index/index'
          })
        }
      }
    })
  },

  async loadUserData() {
    if (!app.globalData.openid) {
      return
    }

    try {
      const db = wx.cloud.database()
      
      // 获取用户信息
      const userRes = await db.collection('users').where({
        _openid: app.globalData.openid
      }).get()
      
      if (userRes.data.length > 0) {
        const userData = userRes.data[0]
        this.setData({
          userInfo: userData,
          hasUserInfo: true,
          points: userData.points || 0,
          level: this.calculateLevel(userData.points || 0)
        })
      }
      
      // 获取收藏列表
      const favRes = await db.collection('user_favorites')
        .where({
          _openid: app.globalData.openid
        })
        .orderBy('createTime', 'desc')
        .get()
      
      // 获取收藏的帖子详情
      const postIds = favRes.data.map(item => item.postId)
      const posts = await db.collection('posts')
        .where({
          _id: db.command.in(postIds)
        })
        .get()
      
      this.setData({
        favorites: posts.data.map(post => ({
          id: post._id,
          title: post.title,
          createTime: this.formatTime(post.createTime)
        }))
      })
      
      // 获取测算记录
      const recordRes = await db.collection('divination_records')
        .where({
          _openid: app.globalData.openid
        })
        .orderBy('createTime', 'desc')
        .get()
      
      this.setData({
        records: recordRes.data.map(record => ({
          id: record._id,
          type: record.type,
          result: record.result,
          createTime: this.formatTime(record.createTime)
        }))
      })
    } catch (error) {
      console.error('加载用户数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  calculateLevel(points) {
    // 等级计算规则：每100点升一级
    return Math.floor(points / 100) + 1
  },

  formatTime(timestamp) {
    if (!timestamp) return ''
    const date = new Date(timestamp)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  },

  getUserProfile(e) {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
        this.updateUserInfo(res.userInfo)
      }
    })
  },

  async updateUserInfo(userInfo) {
    try {
      const db = wx.cloud.database()
      await db.collection('users').where({
        _openid: app.globalData.openid
      }).update({
        data: {
          ...userInfo,
          updateTime: new Date()
        }
      })
    } catch (error) {
      console.error('更新用户信息失败:', error)
    }
  },

  switchTab(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({
      activeTab: tab
    })
  },

  toggleTheme() {
    const newTheme = this.data.settings.theme === 'light' ? 'dark' : 'light'
    this.setData({
      'settings.theme': newTheme
    })
    wx.setStorageSync('theme', newTheme)
  },

  toggleNotification() {
    this.setData({
      'settings.notification': !this.data.settings.notification
    })
    wx.setStorageSync('notification', this.data.settings.notification)
  },

  // 查看收藏的帖子
  viewFavorite(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${id}`
    })
  },

  // 查看测算记录详情
  viewRecord(e) {
    const { id, type } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/${type}/detail?id=${id}`
    })
  },

  // 提交反馈
  submitFeedback(e) {
    const { content } = e.detail.value
    if (!content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      })
      return
    }

    const db = wx.cloud.database()
    db.collection('feedback').add({
      data: {
        content: content.trim(),
        createTime: new Date()
      }
    }).then(() => {
      wx.showToast({
        title: '反馈成功',
        icon: 'success'
      })
    }).catch(error => {
      console.error('提交反馈失败:', error)
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      })
    })
  }
})