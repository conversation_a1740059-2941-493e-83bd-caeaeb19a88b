/* pages/birth-info/birth-info.wxss */
.container {
  padding: 30rpx;
  background-color: var(--background-color) !important;
  min-height: 100vh;
}

.form-section {
  background-color: var(--card-background);
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx var(--shadow-color);
  border: 2rpx solid var(--border-color);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 10rpx;
}

.picker {
  background-color: var(--primary-lightest);
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
}

.radio-group {
  display: flex;
  gap: 30rpx;
}

.radio {
  font-size: 28rpx;
  color: var(--text-primary);
}

.save-btn {
  margin-top: 40rpx;
  background-color: var(--primary-color);
  color: #FFFFFF;
  font-size: 30rpx;
  padding: 20rpx 0;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx var(--shadow-color);
}

.save-btn:active {
  opacity: 0.8;
  transform: translateY(2rpx);
}

.input {
  background-color: var(--primary-lightest);
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
} 