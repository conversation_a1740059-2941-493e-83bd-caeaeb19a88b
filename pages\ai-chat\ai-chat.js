const app = getApp()
const apiService = require('../../utils/api-service')
const config = require('../../config/config')

Page({
  data: {
    messages: [],
    inputValue: '',
    isTyping: false,
    currentTypingMessage: '',
    scrollTop: 0,
    isLoggedIn: false,
    userInfo: null,
    quickActions: [
      { 
        id: 'bazi', 
        title: '八字分析', 
        icon: '🔮',
        query: '请帮我做一个八字分析'
      },
      { 
        id: 'yijing', 
        title: '易经卦象', 
        icon: '☯️',
        query: '请帮我解读一下易经卦象'
      },
      { 
        id: 'fengshui', 
        title: '风水分析', 
        icon: '🏠',
        query: '请帮我分析一下房屋风水'
      },
      { 
        id: 'wuxing', 
        title: '五行分析', 
        icon: '🌿',
        query: '请帮我做一个五行分析'
      }
    ],
    showQuickActions: true,
    messageHistory: [],
    isInputExpanded: false,
    fabIcon: '💬'
  },

  onLoad() {
    this.initializeChat()
  },

  onShow() {
    try {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 1
        })
      }
    } catch (error) {
      console.error('设置 TabBar 选中状态失败:', error)
    }
    this.checkLoginStatus()
  },

  /**
   * 初始化聊天界面
   */
  async initializeChat() {
    this.setData({
      isLoggedIn: apiService.isLoggedIn()
    })

    // 加载历史消息
    this.loadMessageHistory()

    // 如果没有历史消息，添加初始化数据
    if (this.data.messages.length === 0) {
      this.setData({ isTyping: true })
      
      try {
        const initialMessages = [
          '欢迎使用AI命理分析助手！我可以为您提供专业的命理分析和玄学咨询。',
          '您可以选择下方的快捷功能，或直接告诉我您想了解什么。',
          '比如：八字分析、易经卦象、风水分析、五行分析等。'
        ]
        
        // 使用typeMessage函数来依次展示消息
        await this.typeMessage(initialMessages)
      } catch (error) {
        console.error('展示初始消息失败:', error)
        this.handleError(error)
      } finally {
        this.setData({ isTyping: false })
      }
    }
  },

  /**
   * 获取欢迎消息
   */
  getWelcomeMessage() {
    if (this.data.isLoggedIn) {
      return '🔮 欢迎回到卦里乾坤！我是你的玄学AI助手，可以为你提供：\n\n• 八字命理分析\n• 易经卦象解读\n• 风水布局建议\n• 五行运势分析\n• 日常占卜咨询\n\n请直接说出你想了解的内容，或者点击下方快捷功能开始吧！'
    } else {
      return '✨ 你好！我是卦里乾坤的AI助手。为了提供更精准的命理分析，建议先登录获取完整功能。\n\n当前可以为你提供基础的玄学咨询，如需个性化分析请先登录。'
    }
  },

  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    const isLoggedIn = apiService.isLoggedIn()
    this.setData({ isLoggedIn })

    if (isLoggedIn) {
      try {
        const userInfo = wx.getStorageSync('user_info')
        this.setData({ userInfo })
      } catch (error) {
        console.warn('获取用户信息失败:', error)
      }
    }
  },

  /**
   * 微信登录
   */
  async handleWxLogin() {
    try {
      wx.showLoading({ title: '登录中...' })
      
      const userInfo = await apiService.wxLogin()
      this.setData({ 
        isLoggedIn: true,
        userInfo 
      })

      this.addMessage({
        type: 'system',
        content: `🎉 登录成功！欢迎 ${userInfo.nickname || '用户'}，现在可以享受完整的命理分析功能了。`
      })

      wx.hideLoading()
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })
    } catch (error) {
      wx.hideLoading()
      console.error('登录失败:', error)
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 退出登录
   */
  handleLogout() {
    wx.showModal({
      title: '确认退出',
      content: '退出后将无法使用个性化功能，确定要退出吗？',
      success: (res) => {
        if (res.confirm) {
          apiService.logout()
          this.setData({ 
            isLoggedIn: false,
            userInfo: null 
          })
          this.addMessage({
            type: 'system',
            content: '👋 已退出登录，感谢使用卦里乾坤！'
          })
        }
      }
    })
  },

  /**
   * 输入框聚焦时展开
   */
  onInputFocus() {
    this.setData({
      isInputExpanded: true
    });
  },

  /**
   * 输入框失焦时，如果内容为空则收起
   */
  onInputBlur() {
    // 延时判断，避免立即收起导致发送按钮无法点击
    setTimeout(() => {
      if (!this.data.inputValue.trim()) {
        this.setData({
          isInputExpanded: false
        });
      }
    }, 100); // 100毫秒延时，可以根据情况调整
  },

  /**
   * 点击悬浮球展开 (如果需要点击球体也展开的话)
   */
  onFabClick() {
    this.setData({
      isInputExpanded: true
    });
    // 如果希望点击球后输入框自动聚焦，需要额外处理
    // 因为textarea的focus行为在小程序中不总是直接响应setData后的状态变化
  },

  /**
   * 输入框内容变化处理
   */
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    })
  },

  /**
   * 发送消息
   */
  async sendMessage() {
    const { inputValue } = this.data
    if (!inputValue.trim()) {
      this.setData({ isInputExpanded: false })
      return
    }

    // 添加用户消息
    this.addMessage({
      type: 'user',
      content: inputValue,
      timestamp: this.formatTime(new Date())
    })

    this.setData({
      inputValue: '', 
      showQuickActions: false,
      isInputExpanded: false
    })

    this.scrollToBottom()
    this.setData({ isTyping: true })
    
    try {
      // 获取AI回复内容
      const responses = await this.mockAIResponse(inputValue)
      // 依次展示回复
      await this.typeMessage(responses)
    } catch (error) {
      console.error('AI回复失败:', error)
      this.handleError(error)
    } finally {
      this.setData({ isTyping: false })
    }
  },

  /**
   * 处理快捷操作点击
   */
  async onQuickActionTap(e) {
    const { id } = e.currentTarget.dataset
    const action = this.data.quickActions.find(item => item.id === id)
    
    if (action && action.query) {
      // 添加用户消息
      this.addMessage({
        type: 'user',
        content: action.query,
        timestamp: this.formatTime(new Date())
      })

      this.setData({ 
        showQuickActions: false,
        isTyping: true
      })

      try {
        // 根据不同的快捷操作类型返回不同的初始化问题
        let responses = []
        switch (id) {
          case 'bazi':
            responses = [
              '好的，让我来为您进行八字分析。',
              '首先，我需要了解您的基本信息。',
              '请问您的出生年月日是？（例如：1990年8月18日）'
            ]
            break
          case 'yijing':
            responses = [
              '我将为您解读易经卦象。',
              '请在心中默想您的问题，',
              '然后告诉我您想了解哪个方面？（事业/感情/财运）'
            ]
            break
          case 'fengshui':
            responses = [
              '好的，我来为您分析房屋风水。',
              '请先告诉我您的房屋朝向？（例如：坐北朝南）'
            ]
            break
          case 'wuxing':
            responses = [
              '我将为您进行五行分析。',
              '请先告诉我您的出生年月日，',
              '以便我能准确分析您的五行属性。'
            ]
            break
        }
        
        // 依次展示回复
        await this.typeMessage(responses)
      } catch (error) {
        console.error('AI回复失败:', error)
        this.handleError(error)
      } finally {
        this.setData({ isTyping: false })
      }
    }
  },

  /**
   * 添加消息到列表
   */
  addMessage(message) {
    const messages = [...this.data.messages, {
      ...message,
      id: Date.now() + Math.random()
    }]
    
    this.setData({ messages }, () => {
      this.scrollToBottom()
      this.saveMessageHistory()
    })
  },

  /**
   * 模拟AI回复
   * @param {string} userInput 用户输入
   */
  async mockAIResponse(userInput) {
    // 根据用户输入内容生成回复
    if (userInput.includes('出生')) {
      return [
        '好的，我已经记录下您的出生信息。',
        '接下来，请问您的出生时辰是？（例如：子时23-1点，丑时1-3点）',
      ]
    } else if (userInput.includes('时') || userInput.includes('点')) {
      return [
        '非常好，现在我已经掌握了您的完整八字信息。',
        '让我为您详细分析：',
        '1. 您的八字格局...',
        '2. 您的五行属性...',
        '3. 吉凶分析...',
        '建议您可以...'
      ]
    } else {
      return ['抱歉，我没有理解您的问题。请问您的出生年月日是？（例如：1990年8月18日）']
    }
  },

  /**
   * 依次展示AI回复
   * @param {Array|string} responses AI回复内容
   */
  async typeMessage(responses) {
    const messages = Array.isArray(responses) ? responses : [responses]
    
    for (let i = 0; i < messages.length; i++) {
      await this.simulateTyping(800) // 思考时间
      
      const message = {
        type: 'ai',
        content: messages[i],
        timestamp: this.formatTime(new Date())
      }
      
      this.addMessage(message)
      await this.scrollToBottom()
      
      // 如果不是最后一条消息，添加一个短暂延迟
      if (i < messages.length - 1) {
        await this.sleep(500)
      }
    }
  },

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  /**
   * 模拟打字延迟
   */
  simulateTyping(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  /**
   * 处理错误
   */
  handleError(error) {
    this.addMessage({
      type: 'ai',
      content: '抱歉，我现在遇到了一些问题。请稍后再试。',
      timestamp: this.formatTime(new Date())
    })
  },

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    // 使用 nextTick 确保DOM更新后再滚动
    wx.nextTick(() => {
      // 创建查询对象
      const query = wx.createSelectorQuery().in(this);
      
      // 查询scroll-view的高度
      query.select('.chat-messages').node();
      query.select('.chat-messages').scrollOffset();
      
      query.exec((res) => {
        if (res[0] && res[1]) {
          // 获取scroll-view的实际内容高度
          const scrollHeight = res[0].scrollHeight || 0;
          const height = res[1].height || 0;
          
          // 计算需要滚动的距离
          const scrollTop = scrollHeight - height;
          
          if (scrollTop > 0) {
          this.setData({
              scrollTop: scrollTop + 1000 // 加一个额外的值确保滚动到底
            });
          }
        }
      });
    });
  },

  /**
   * 格式化时间
   */
  formatTime(date) {
    const hour = date.getHours()
    const minute = date.getMinutes()
    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
  },

  /**
   * 保存消息历史
   */
  saveMessageHistory() {
    try {
      const messages = this.data.messages.slice(-config.features.maxMessageHistory)
      wx.setStorageSync('chat_history', messages)
    } catch (error) {
      console.warn('保存聊天记录失败:', error)
    }
  },

  /**
   * 加载消息历史
   */
  loadMessageHistory() {
    try {
      const messages = wx.getStorageSync('chat_history') || []
      this.setData({ messages })
    } catch (error) {
      console.warn('加载聊天记录失败:', error)
    }
  },

  /**
   * 清空聊天记录
   */
  clearChatHistory() {
    wx.showModal({
      title: '清空聊天记录',
      content: '确定要清空所有聊天记录吗？此操作无法撤销。',
      success: (res) => {
        if (res.confirm) {
          this.setData({ 
            messages: [],
            showQuickActions: true 
          })
          wx.removeStorageSync('chat_history')
          
          // 重新添加欢迎消息
          this.addMessage({
            type: 'ai',
            content: this.getWelcomeMessage(),
            messageType: 'welcome'
          })
          
          wx.showToast({
            title: '聊天记录已清空',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 处理消息操作按钮点击
   */
  onMessageAction(e) {
    const { action } = e.currentTarget.dataset
    
    switch (action) {
      case 'goto_birth_info':
        wx.navigateTo({
          url: '/pages/birth-info/birth-info'
        })
        break
      case 'retry':
        // 重试最后一条用户消息
        const lastUserMessage = this.data.messages
          .slice()
          .reverse()
          .find(msg => msg.type === 'user')
        if (lastUserMessage) {
          this.retryMessage(lastUserMessage.content)
        }
        break
    }
  },

  /**
   * 重试消息
   */
  async retryMessage(content) {
    this.setData({ isTyping: true })
    
    try {
      const response = await apiService.intelligentQuery(
        content,
        this.data.userInfo
      )
      
      this.setData({ isTyping: false })
      this.handleApiResponse(response, content)
    } catch (error) {
      this.setData({ isTyping: false })
      this.handleApiError(error, content)
    }
  },

  /**
   * 打字机效果函数
   * @param {String} text - 要显示的文本
   * @param {Number} messageIndex - 消息在数组中的索引
   * @param {Number} speed - 打字速度（毫秒）
   */
  typewriterEffect(text, messageIndex, speed = 50) {
    return new Promise((resolve) => {
      let charIndex = 0;
      const messageKey = `messages[${messageIndex}].content`;
      
      // 先设置空内容
      this.setData({
        [messageKey]: '',
        [`messages[${messageIndex}].isTyping`]: true
      });
      
      const typeInterval = setInterval(() => {
        if (charIndex < text.length) {
          // 逐字添加
          const currentText = text.substring(0, charIndex + 1);
          this.setData({
            [messageKey]: currentText
          });
          charIndex++;
        } else {
          // 打字完成
          clearInterval(typeInterval);
          this.setData({
            [`messages[${messageIndex}].isTyping`]: false
          });
          resolve();
        }
      }, speed);
    });
  },
  
  /**
   * 处理AI响应的函数
   */
  async handleAIResponse(responseText) {
    // 隐藏输入中状态
    this.setData({ isTyping: false });
    
    // 创建AI消息对象
    const aiMessage = {
      id: Date.now(),
      type: 'ai',
      content: '', // 初始为空，通过打字机效果逐渐显示
      timestamp: this.formatTime(new Date()),
      isTyping: true
    };
    
    // 添加消息到列表
    const messages = this.data.messages;
    messages.push(aiMessage);
    const messageIndex = messages.length - 1;
    
    this.setData({ 
      messages,
      showQuickActions: false 
    });
    
    // 滚动到底部
    this.scrollToBottom();
    
    // 执行打字机效果
    await this.typewriterEffect(responseText, messageIndex, 30);
    
    // 打字机效果完成后再次滚动到底部
    this.scrollToBottom();
  },
}) 