const app = getApp()
const wxworkUtil = app.globalData.wxworkUtil

Page({
  data: {
    contacts: [],
    departments: [],
    currentDepartment: '',
    searchKeyword: '',
    loading: false,
    showFabMenu: false,
    originalContacts: [] // 用于搜索时的原始数据备份
  },

  onLoad() {
    this.loadDepartments()
    this.loadContacts()
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadContacts()
  },

  // 加载部门列表
  async loadDepartments() {
    try {
      const departments = await wxworkUtil.getDepartmentList()
      this.setData({ departments })
    } catch (error) {
      console.error('加载部门列表失败:', error)
      wxworkUtil.showError('加载部门列表失败')
    }
  },

  // 加载联系人列表
  async loadContacts(departmentId = '') {
    this.setData({ loading: true })
    
    try {
      const contacts = await wxworkUtil.getContactList(departmentId)
      this.setData({ 
        contacts,
        originalContacts: contacts,
        loading: false 
      })
    } catch (error) {
      console.error('加载联系人失败:', error)
      wxworkUtil.showError('加载联系人失败')
      this.setData({ loading: false })
    }
  },

  // 选择部门
  selectDepartment(e) {
    const departmentId = e.currentTarget.dataset.id
    this.setData({ 
      currentDepartment: departmentId,
      searchKeyword: ''
    })
    this.loadContacts(departmentId)
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })
    this.searchContacts(keyword)
  },

  // 搜索确认
  onSearchConfirm(e) {
    const keyword = e.detail.value
    this.searchContacts(keyword)
  },

  // 搜索联系人
  searchContacts(keyword) {
    if (!keyword.trim()) {
      // 如果搜索关键词为空，恢复原始数据
      this.setData({ contacts: this.data.originalContacts })
      return
    }

    const filteredContacts = this.data.originalContacts.filter(contact => {
      const name = contact.name || ''
      const department = contact.department ? contact.department.join(' ') : ''
      const position = contact.position || ''
      const mobile = contact.mobile || ''
      
      return name.includes(keyword) || 
             department.includes(keyword) || 
             position.includes(keyword) || 
             mobile.includes(keyword)
    })

    this.setData({ contacts: filteredContacts })
  },

  // 查看联系人详情
  viewContactDetail(e) {
    const contact = e.currentTarget.dataset.contact
    wx.navigateTo({
      url: `/pages/contact-detail/contact-detail?userId=${contact.userid}`
    })
  },

  // 开始聊天 - 更新为支持企业微信聊天
  startChat(e) {
    const contact = e.currentTarget.dataset.contact
    
    // 检查是否有发送消息权限
    if (!wxworkUtil.checkPermission('sendMessages')) {
      wxworkUtil.showError('没有发送消息的权限')
      return
    }

    // 显示聊天选项
    wx.showActionSheet({
      itemList: ['企业微信聊天', 'AI智能聊天', '普通聊天'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0: // 企业微信聊天
            this.startWxWorkChat(contact)
            break
          case 1: // AI智能聊天
            this.startAIChat(contact)
            break
          case 2: // 普通聊天
            this.startNormalChat(contact)
            break
        }
      }
    })
  },

  // 启动企业微信聊天
  startWxWorkChat(contact) {
    if (!app.globalData.isWxWork) {
      wx.showModal({
        title: '提示',
        content: '企业微信聊天需要在企业微信环境中使用',
        showCancel: false
      })
      return
    }

    // 跳转到企业微信聊天页面
    wx.navigateTo({
      url: `/pages/wxwork-chat/wxwork-chat?userId=${contact.userid}`
    })
  },

  // 启动AI智能聊天
  startAIChat(contact) {
    // 跳转到AI聊天页面，传入用户信息
    wx.navigateTo({
      url: `/pages/ai-chat/ai-chat?targetUser=${JSON.stringify(contact)}`
    })
  },

  // 启动普通聊天
  startNormalChat(contact) {
    wx.navigateTo({
      url: `/pages/chat/chat?userId=${contact.userid}&userName=${contact.name}`
    })
  },

  // 拨打电话
  makeCall(e) {
    const phone = e.currentTarget.dataset.phone
    if (!phone) return

    wx.showModal({
      title: '拨打电话',
      content: `确定要拨打 ${phone} 吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phone,
            success: () => {
              console.log('拨打电话成功')
            },
            fail: (error) => {
              console.error('拨打电话失败:', error)
              wxworkUtil.showError('拨打电话失败')
            }
          })
        }
      }
    })
  },

  // 显示快速操作菜单
  showFabMenu() {
    this.setData({ showFabMenu: true })
  },

  // 隐藏快速操作菜单
  hideFabMenu() {
    this.setData({ showFabMenu: false })
  },

  // 添加联系人
  addContact() {
    this.hideFabMenu()
    
    if (!wxworkUtil.isAdmin()) {
      wxworkUtil.showError('只有管理员可以添加联系人')
      return
    }

    wx.navigateTo({
      url: '/pages/contact-add/contact-add'
    })
  },

  // 管理部门
  manageDepartments() {
    this.hideFabMenu()
    
    if (!wxworkUtil.isAdmin()) {
      wxworkUtil.showError('只有管理员可以管理部门')
      return
    }

    wx.navigateTo({
      url: '/pages/department/department'
    })
  },

  // 快速AI聊天 - 新增功能
  quickAIChat() {
    this.hideFabMenu()
    
    wx.showModal({
      title: 'AI智能助手',
      content: '是否要与AI助手开始对话？AI将为您提供个性化的命理分析服务。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/ai-chat/ai-chat?mode=enterprise'
          })
        }
      }
    })
  },

  // 批量发送AI消息 - 新增功能
  batchSendAIMessage() {
    this.hideFabMenu()
    
    if (!wxworkUtil.isAdmin()) {
      wxworkUtil.showError('只有管理员可以批量发送消息')
      return
    }

    wx.navigateTo({
      url: '/pages/batch-message/batch-message'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadContacts(this.data.currentDepartment)
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '卦里乾坤企业版 - 通讯录',
      path: '/pages/contacts/contacts',
      imageUrl: '/assets/images/share-contacts.png'
    }
  }
}) 