# 卦里乾坤小程序后台管理界面需求文档

## 📋 概述

基于API接口文档，设计一个现代化的后台管理界面，用于管理小程序的用户、内容、数据分析和系统配置。

## 🎯 核心功能模块

### 1. 仪表盘 (Dashboard)

#### 数据概览卡片
- **用户统计**
  - 总用户数、新增用户、活跃用户、VIP用户
  - 用户增长趋势图表
  - 用户地域分布图

- **业务统计**
  - 总分析次数、今日分析、热门分析类型
  - 分析类型分布饼图
  - 分析趋势折线图

- **收入统计**
  - 总收入、今日收入、积分销售、VIP收入
  - 收入趋势图表
  - 收入来源分析

- **系统状态**
  - API调用次数、错误率、平均响应时间
  - 存储使用量、服务器状态
  - 实时监控图表

#### 快捷操作
- 用户管理入口
- 系统配置入口
- 数据导出功能
- 系统公告发布

### 2. 用户管理 (User Management)

#### 用户列表
- **筛选功能**
  - 按状态筛选 (正常/禁用/删除)
  - 按VIP等级筛选
  - 按注册时间范围筛选
  - 按地域筛选

- **搜索功能**
  - 按昵称搜索
  - 按手机号搜索
  - 按邮箱搜索

- **列表字段**
  - 用户头像、昵称、手机号
  - VIP等级、积分余额
  - 注册时间、最后登录时间
  - 分析次数、总消费
  - 状态、操作按钮

#### 用户详情
- **基本信息**
  - 个人资料、联系方式
  - 注册信息、登录记录
  - VIP信息、积分记录

- **出生信息**
  - 姓名、性别、出生时间
  - 八字信息、五行分析
  - 生肖、星座、幸运数字

- **使用记录**
  - 分析历史记录
  - AI聊天记录
  - 积分获取/消费记录

- **操作功能**
  - 编辑用户信息
  - 调整积分余额
  - 设置VIP等级
  - 禁用/启用账户

### 3. 分析记录管理 (Analysis Management)

#### 记录列表
- **筛选功能**
  - 按分析类型筛选
  - 按时间范围筛选
  - 按用户筛选
  - 按评分范围筛选

- **列表字段**
  - 用户信息、分析类型
  - 分析评分、消耗积分
  - 处理时间、创建时间
  - 分享状态、操作按钮

#### 记录详情
- **输入数据**
  - 用户提供的原始数据
  - 分析参数和选项

- **分析结果**
  - 完整的分析报告
  - 评分和建议
  - 生成的图表和数据

- **统计信息**
  - 处理时间、API调用
  - 分享次数、用户反馈

### 4. AI聊天管理 (Chat Management)

#### 会话管理
- **会话列表**
  - 用户信息、会话标题、状态
  - 消息数量、token消耗、成本
  - 开始时间、结束时间、持续时长
  - 初始意图、最后活动时间

- **会话详情**
  - 完整对话记录展示
  - 消息时间线和类型标识
  - 意图识别结果和置信度
  - Token使用统计和成本分析

- **会话操作**
  - 会话状态管理 (活跃/结束/归档)
  - 批量删除和导出
  - 会话质量评估
  - 用户满意度统计

#### 消息分析
- **消息详情**
  - 消息内容、类型、时间戳
  - 意图识别过程和结果
  - AI回复生成过程
  - 用户反馈和评分

- **质量评估**
  - 回复准确性评分
  - 响应时间统计
  - 用户满意度分析
  - 问题解决率统计

#### AI模型配置
- **模型管理**
  - 当前模型信息和版本
  - 可用模型列表和切换
  - 模型性能对比
  - 成本效益分析

- **参数调优**
  - Temperature、Top-p设置
  - Max tokens、频率惩罚
  - 上下文长度配置
  - 实时参数测试

- **API配置**
  - API端点和密钥管理
  - 请求限制和超时设置
  - 重试策略配置
  - 错误处理规则

#### 意图识别管理
- **意图配置**
  - 意图名称和描述
  - 关键词和模式管理
  - 置信度阈值设置
  - 优先级和状态控制

- **回复模板**
  - 模板内容编辑
  - 变量和占位符
  - 多版本管理
  - A/B测试支持

- **训练数据**
  - 样本数据管理
  - 标注和分类
  - 模型训练历史
  - 准确率统计

#### 性能监控
- **实时监控**
  - 当前活跃会话数
  - 实时响应时间
  - API调用状态
  - 错误率监控

- **统计分析**
  - 会话量趋势图
  - 意图分布统计
  - 用户满意度趋势
  - 成本分析报告

- **告警设置**
  - 响应时间告警
  - 错误率告警
  - 成本超限告警
  - 服务异常通知

### 5. 积分系统管理 (Points Management)

#### 积分配置
- **获取规则**
  - 签到奖励设置
  - 分享奖励设置
  - 邀请奖励设置

- **消费规则**
  - 各类分析消费积分
  - VIP折扣设置
  - 特殊活动价格

#### 积分记录
- **记录查询**
  - 按用户查询
  - 按类型筛选
  - 按时间范围筛选

- **批量操作**
  - 批量发放积分
  - 积分回收
  - 过期积分清理

### 6. 企业微信管理 (WeWork Management)

#### 企业管理
- **企业列表**
  - 企业信息、状态
  - 用户数量、活跃度
  - 接入时间、最后同步

- **部门管理**
  - 部门结构树
  - 成员管理
  - 权限配置

#### 消息管理
- **群发消息**
  - 消息编辑器
  - 接收者选择
  - 发送状态跟踪

- **消息记录**
  - 历史消息查看
  - 发送统计
  - 用户反馈

### 7. 知识库管理 (Knowledge Management)

#### 文档管理
- **文档列表**
  - 文档标题、分类、状态
  - 作者、发布时间、更新时间
  - 查看次数、点赞数、评分
  - 批量操作和状态管理

- **文档编辑**
  - 富文本编辑器 (支持Markdown)
  - 分类和标签管理
  - 难度等级设置
  - 关键词和摘要生成
  - 预览和发布功能

- **文档分析**
  - 阅读量统计
  - 用户反馈分析
  - 搜索排名监控
  - 内容质量评估

#### 问答对管理
- **问答列表**
  - 问题、答案、分类
  - 使用次数、成功率
  - 置信度评分、状态
  - 审核和质量管理

- **问答编辑**
  - 问题和答案编辑
  - 分类和难度设置
  - 关联文档管理
  - 相似问题检测

- **智能优化**
  - 自动问题聚类
  - 答案质量评估
  - 使用效果分析
  - 优化建议生成

#### 搜索管理
- **搜索统计**
  - 搜索量趋势
  - 热门查询词
  - 零结果查询
  - 用户满意度

- **搜索优化**
  - 关键词管理
  - 同义词配置
  - 搜索结果调优
  - 个性化推荐

- **向量管理**
  - 向量嵌入状态
  - 重新生成任务
  - 相似度调优
  - 性能监控

#### 分类管理
- **分类结构**
  - 分类树形管理
  - 层级关系设置
  - 排序和显示控制
  - 图标和描述管理

- **内容分布**
  - 各分类内容统计
  - 使用频率分析
  - 热门分类识别
  - 内容缺口分析

### 8. 系统配置 (System Configuration)

#### 基础配置
- **应用设置**
  - 应用名称、Logo
  - 联系方式、客服信息
  - 隐私政策、用户协议

- **功能开关**
  - 注册开关
  - 维护模式
  - 功能模块启用/禁用

#### 高级配置
- **API配置**
  - 第三方服务配置
  - 接口限流设置
  - 缓存配置

- **安全设置**
  - 密码策略
  - 登录限制
  - 数据备份设置

## 🎨 界面设计要求

### 设计风格
- **现代化设计**：采用Material Design或Ant Design风格
- **响应式布局**：支持桌面端和移动端访问
- **深色模式**：支持明暗主题切换
- **国际化**：支持中英文切换

### 交互体验
- **快速加载**：页面加载时间 < 2秒
- **流畅动画**：页面切换和操作反馈动画
- **智能提示**：操作引导和错误提示
- **批量操作**：支持多选和批量处理

### 数据可视化
- **图表组件**：使用ECharts或Chart.js
- **实时更新**：关键数据实时刷新
- **数据导出**：支持Excel、PDF导出
- **自定义报表**：可配置的数据报表

## 🔧 技术要求

### 前端技术栈
- **框架**：React 18+ 或 Vue 3+
- **UI库**：Ant Design 或 Element Plus
- **状态管理**：Redux/Zustand 或 Pinia
- **路由**：React Router 或 Vue Router
- **HTTP客户端**：Axios
- **图表库**：ECharts 或 Chart.js

### 开发规范
- **TypeScript**：全面使用TypeScript开发
- **代码规范**：ESLint + Prettier
- **组件化**：高度组件化开发
- **测试**：单元测试覆盖率 > 80%

### 性能优化
- **代码分割**：路由级别的代码分割
- **懒加载**：组件和图片懒加载
- **缓存策略**：合理的缓存机制
- **打包优化**：Webpack/Vite优化配置

## 📱 页面结构

### 布局结构
```
├── 登录页面
├── 主框架
│   ├── 顶部导航栏
│   │   ├── Logo和标题
│   │   ├── 用户信息
│   │   ├── 通知中心
│   │   └── 设置菜单
│   ├── 侧边导航栏
│   │   ├── 仪表盘
│   │   ├── 用户管理
│   │   ├── 分析记录
│   │   ├── AI聊天
│   │   ├── 知识库管理
│   │   ├── 积分系统
│   │   ├── 企业微信
│   │   └── 系统配置
│   └── 主内容区域
│       ├── 面包屑导航
│       ├── 页面标题和操作
│       └── 内容区域
└── 全局组件
    ├── 加载动画
    ├── 确认对话框
    ├── 通知提示
    └── 错误页面
```

### 权限控制
- **角色管理**：超级管理员、管理员、操作员
- **菜单权限**：基于角色的菜单显示
- **操作权限**：按钮级别的权限控制
- **数据权限**：基于角色的数据访问控制

## 📊 数据展示

### 图表类型
- **折线图**：趋势分析 (用户增长、收入趋势)
- **柱状图**：对比分析 (分析类型分布)
- **饼图**：占比分析 (用户地域分布)
- **热力图**：活跃度分析 (用户活跃时间)
- **仪表盘**：实时监控 (系统状态)

### 数据表格
- **分页**：支持前端和后端分页
- **排序**：多字段排序
- **筛选**：高级筛选功能
- **导出**：数据导出功能
- **操作**：行内操作按钮

## 🚀 部署要求

### 环境要求
- **Node.js**：16+
- **包管理器**：npm 或 yarn
- **构建工具**：Webpack 或 Vite
- **Web服务器**：Nginx

### 部署流程
1. **构建**：npm run build
2. **测试**：自动化测试
3. **部署**：自动化部署
4. **监控**：性能监控

---

## 📝 总结

本文档详细描述了卦里乾坤小程序后台管理界面的完整需求，包括功能模块、界面设计、技术要求和部署说明。管理界面将提供全面的数据管理、用户管理、系统配置等功能，为运营团队提供高效的管理工具。
