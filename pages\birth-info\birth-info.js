Page({
  data: {
    name: '',
    gender: '男',
    zodiacList: ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'],
    zodiacIndex: null,
    numberList: Array.from({length: 62}, (_, i) => i + 1),
    numberIndex: null,
    dateTime: [],
    dateTimeArray: [],
    selectedDateTime: '',
    years: [],
    months: [],
    days: [],
    hours: [],
    minutes: []
  },

  onLoad() {
    // 初始化日期时间选择器数据
    this.initDateTimePickerData();
    
    // 从本地存储读取出生信息
    const birthInfo = wx.getStorageSync('birthInfo');
    if (birthInfo) {
      this.setData({
        name: birthInfo.name || '',
        gender: birthInfo.gender || '男',
        zodiacIndex: birthInfo.zodiacIndex || null,
        numberIndex: birthInfo.numberIndex || null,
        selectedDateTime: birthInfo.selectedDateTime || ''
      });

      // 如果有已保存的日期时间，设置选择器的值
      if (birthInfo.dateTimeArray) {
        this.setData({
          dateTimeArray: birthInfo.dateTimeArray
        });
      }
    }
  },

  initDateTimePickerData() {
    const date = new Date();
    const years = [];
    const months = [];
    const days = [];
    const hours = [];
    const minutes = [];

    // 生成年份列表（从1900年到当前年份）
    for (let i = 1900; i <= date.getFullYear(); i++) {
      years.push(i + '年');
    }

    // 生成月份列表
    for (let i = 1; i <= 12; i++) {
      months.push(i + '月');
    }

    // 生成天数列表（默认31天）
    for (let i = 1; i <= 31; i++) {
      days.push(i + '日');
    }

    // 生成小时列表
    for (let i = 0; i < 24; i++) {
      hours.push(i + '时');
    }

    // 生成分钟列表
    for (let i = 0; i < 60; i++) {
      minutes.push(i + '分');
    }

    this.setData({
      dateTime: [years, months, days, hours, minutes],
      years,
      months,
      days,
      hours,
      minutes,
      dateTimeArray: [0, 0, 0, 0, 0] // 默认选中当前时间
    });
  },

  updateDays(year, month) {
    const days = [];
    const daysInMonth = new Date(year, month, 0).getDate();
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(i + '日');
    }
    return days;
  },

  onDateTimeColumnChange(e) {
    const { column, value } = e.detail;
    const { dateTime, dateTimeArray, years, months } = this.data;
    const newDateTimeArray = [...dateTimeArray];
    newDateTimeArray[column] = value;

    // 如果修改了年份或月份，需要更新天数
    if (column === 0 || column === 1) {
      const year = parseInt(years[newDateTimeArray[0]]);
      const month = parseInt(months[newDateTimeArray[1]]);
      const newDays = this.updateDays(year, month);
      const newDateTime = [...dateTime];
      newDateTime[2] = newDays;
      
      this.setData({
        dateTime: newDateTime
      });
    }

    this.setData({
      dateTimeArray: newDateTimeArray
    });
  },

  onDateTimeChange(e) {
    const { value } = e.detail;
    const { dateTime } = this.data;
    
    const selectedDateTime = dateTime.map((arr, index) => arr[value[index]]).join('');
    
    this.setData({
      dateTimeArray: value,
      selectedDateTime
    });
  },

  onNameInput(e) {
    this.setData({
      name: e.detail.value
    });
  },

  onGenderChange(e) {
    this.setData({
      gender: e.detail.value
    });
  },

  onZodiacChange(e) {
    this.setData({
      zodiacIndex: parseInt(e.detail.value)
    });
  },

  onNumberChange(e) {
    this.setData({
      numberIndex: parseInt(e.detail.value)
    });
  },

  saveInfo() {
    const { 
      name, 
      gender, 
      zodiacIndex, 
      numberIndex, 
      zodiacList, 
      numberList, 
      selectedDateTime,
      dateTime,
      dateTimeArray 
    } = this.data;
    
    if (!name.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }
    
    if (!selectedDateTime) {
      wx.showToast({
        title: '请选择出生日期时间',
        icon: 'none'
      });
      return;
    }

    if (zodiacIndex === null) {
      wx.showToast({
        title: '请选择生肖',
        icon: 'none'
      });
      return;
    }

    if (numberIndex === null) {
      wx.showToast({
        title: '请选择数字',
        icon: 'none'
      });
      return;
    }

    try {
      // 保存到本地存储
      const birthInfo = {
        name: name.trim(),
        gender,
        zodiacIndex,
        numberIndex,
        zodiac: zodiacList[zodiacIndex],
        number: numberList[numberIndex],
        selectedDateTime,
        dateTimeArray,
        dateTime: dateTime.map((arr, index) => arr[dateTimeArray[index]]).join('')
      };
      
      console.log('保存的出生信息:', birthInfo);
      
      wx.setStorageSync('birthInfo', birthInfo);

      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 1500
      });

      // 获取目标页面路径
      const targetPage = wx.getStorageSync('targetPage');
      if (targetPage) {
        // 清除目标页面路径
        wx.removeStorageSync('targetPage');
        // 延迟跳转，让用户看到保存成功的提示
        setTimeout(() => {
          // 检查目标页面是否为TabBar页面
          const tabBarPages = [
            '/pages/index/index',
            '/pages/ai-chat/ai-chat',
            '/pages/profile/profile'
          ];

          if (tabBarPages.includes(targetPage)) {
            wx.switchTab({
              url: targetPage,
              fail: () => {
                // 如果switchTab失败，尝试redirectTo
                wx.redirectTo({ url: targetPage });
              }
            });
          } else {
            wx.redirectTo({
              url: targetPage,
              fail: () => {
                // 如果redirectTo失败，尝试navigateTo
                wx.navigateTo({ url: targetPage });
              }
            });
          }
        }, 1500);
      } else {
        // 如果没有目标页面，返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (e) {
      console.error('保存出生信息失败:', e);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  }
}) 