<!--pages/birth-info/birth-info.wxml-->
<view class="container">
  <view class="form-section">
    <view class="section-title">出生信息</view>
    
    <view class="form-item">
      <text class="label">姓名</text>
      <input class="input" placeholder="请输入您的姓名" bindinput="onNameInput" value="{{name}}" maxlength="20"/>
    </view>
    
    <view class="form-item">
      <text class="label">出生日期时间</text>
      <picker mode="multiSelector" value="{{dateTimeArray}}" bindchange="onDateTimeChange" bindcolumnchange="onDateTimeColumnChange" range="{{dateTime}}">
        <view class="picker">
          {{selectedDateTime || '请选择出生日期时间'}}
        </view>
      </picker>
    </view>

    <view class="form-item">
      <text class="label">生肖</text>
      <picker bindchange="onZodiacChange" value="{{zodiacIndex}}" range="{{zodiacList}}">
        <view class="picker">
          {{zodiacList[zodiacIndex] || '请选择生肖'}}
        </view>
      </picker>
    </view>

    <view class="form-item">
      <text class="label">数字选择</text>
      <picker bindchange="onNumberChange" value="{{numberIndex}}" range="{{numberList}}">
        <view class="picker">
          {{numberList[numberIndex] || '请选择数字(1-62)'}}
        </view>
      </picker>
    </view>

    <view class="form-item">
      <text class="label">性别</text>
      <radio-group class="radio-group" bindchange="onGenderChange">
        <label class="radio">
          <radio value="男" checked="{{gender === '男'}}" color="#6A4FFF"/>男
        </label>
        <label class="radio">
          <radio value="女" checked="{{gender === '女'}}" color="#6A4FFF"/>女
        </label>
      </radio-group>
    </view>

    <button class="save-btn" bindtap="saveInfo">提交</button>
  </view>
</view> 