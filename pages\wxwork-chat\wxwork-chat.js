const app = getApp()
const wxworkUtil = app.globalData.wxworkUtil

Page({
  data: {
    currentChatUser: null,
    userProfile: null,
    messages: [],
    inputText: '',
    isTyping: false,
    inputFocused: false,
    scrollTop: 0,
    scrollIntoView: '',
    welcomeMessage: '',
    welcomeTime: '',
    showProfileModal: false
  },

  onLoad(options) {
    // 初始化企业微信聊天环境
    this.initWxWorkChat()
    
    // 监听聊天用户切换
    this.setupChatUserListener()
    
    // 如果有传入的用户ID，直接加载该用户信息
    if (options.userId) {
      this.loadUserInfo(options.userId)
    }
  },

  onShow() {
    // 每次显示页面时刷新当前聊天用户信息
    this.getCurrentChatUser()
  },

  // 初始化企业微信聊天环境
  async initWxWorkChat() {
    try {
      // 检查是否在企业微信环境中
      if (!app.globalData.isWxWork) {
        wx.showModal({
          title: '提示',
          content: '此功能需要在企业微信环境中使用',
          showCancel: false
        })
        return
      }

      // 获取当前聊天用户
      await this.getCurrentChatUser()
    } catch (error) {
      console.error('初始化企业微信聊天失败:', error)
    }
  },

  // 获取当前聊天用户
  async getCurrentChatUser() {
    try {
      const chatInfo = await wxworkUtil.getCurrentChatUser()
      if (chatInfo && chatInfo.userid) {
        await this.loadUserInfo(chatInfo.userid)
      }
    } catch (error) {
      console.error('获取当前聊天用户失败:', error)
    }
  },

  // 加载用户信息
  async loadUserInfo(userId) {
    try {
      wx.showLoading({ title: '加载用户信息...' })
      
      // 并行获取企业微信用户信息和小程序用户资料
      const [userDetail, userProfile] = await Promise.all([
        wxworkUtil.getUserDetail(userId),
        wxworkUtil.getUserProfileInMiniapp(userId).catch(() => null) // 允许失败
      ])

      this.setData({
        currentChatUser: userDetail,
        userProfile: userProfile
      })

      // 生成个性化欢迎消息
      this.generateWelcomeMessage(userDetail, userProfile)
      
      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      console.error('加载用户信息失败:', error)
      wxworkUtil.showError('加载用户信息失败')
    }
  },

  // 生成个性化欢迎消息
  generateWelcomeMessage(userInfo, userProfile) {
    const welcomeMessage = wxworkUtil.generateAIPromptForUser(userInfo, userProfile)
    const welcomeTime = this.formatTime(new Date())
    
    this.setData({
      welcomeMessage,
      welcomeTime
    })
  },

  // 设置聊天用户变化监听
  setupChatUserListener() {
    wxworkUtil.onChatUserChange(async (chatInfo) => {
      if (chatInfo && chatInfo.userid) {
        // 用户切换时重新加载信息
        await this.loadUserInfo(chatInfo.userid)
        
        // 清空当前对话
        this.setData({ 
          messages: [],
          inputText: '' 
        })
      }
    })
  },

  // 刷新用户信息
  refreshUserInfo() {
    if (this.data.currentChatUser) {
      this.loadUserInfo(this.data.currentChatUser.userid)
    }
  },

  // 查看用户资料
  viewUserProfile() {
    this.setData({ showProfileModal: true })
  },

  // 关闭用户资料弹窗
  closeProfileModal() {
    this.setData({ showProfileModal: false })
  },

  // 生成个性化对话
  async generatePersonalizedChat() {
    this.closeProfileModal()
    
    try {
      wx.showLoading({ title: '生成个性化对话...' })
      
      const context = {
        userInfo: this.data.currentChatUser,
        userProfile: this.data.userProfile,
        chatType: 'personalized_greeting'
      }

      const aiReply = await wxworkUtil.generateAIReply('生成个性化问候和建议', context)
      
      const message = {
        id: Date.now(),
        type: 'ai',
        content: aiReply,
        time: this.formatTime(new Date())
      }

      this.addMessage(message)
      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      console.error('生成个性化对话失败:', error)
      wxworkUtil.showError('生成个性化对话失败')
    }
  },

  // 输入框事件
  onInputChange(e) {
    this.setData({ inputText: e.detail.value })
  },

  onInputFocus() {
    this.setData({ inputFocused: true })
  },

  onInputBlur() {
    this.setData({ inputFocused: false })
  },

  // 发送消息
  async sendMessage() {
    const message = this.data.inputText.trim()
    if (!message || this.data.isTyping) return

    // 添加用户消息
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: message,
      time: this.formatTime(new Date())
    }

    this.addMessage(userMessage)
    this.setData({ 
      inputText: '',
      isTyping: true 
    })

    try {
      // 构建AI对话上下文
      const context = {
        userInfo: this.data.currentChatUser,
        userProfile: this.data.userProfile,
        chatHistory: this.data.messages.slice(-10), // 最近10条消息
        chatType: 'enterprise_chat'
      }

      const aiReply = await wxworkUtil.generateAIReply(message, context)
      
      const aiMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: aiReply,
        time: this.formatTime(new Date())
      }

      this.addMessage(aiMessage)
    } catch (error) {
      console.error('AI回复失败:', error)
      const errorMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: '抱歉，我现在无法回复您的消息，请稍后再试。',
        time: this.formatTime(new Date())
      }
      this.addMessage(errorMessage)
    } finally {
      this.setData({ isTyping: false })
    }
  },

  // 发送AI消息到企业微信聊天
  async sendToWxWorkChat(e) {
    const message = e.currentTarget.dataset.message
    if (!message || !this.data.currentChatUser) return

    try {
      wx.showLoading({ title: '发送中...' })
      
      await wxworkUtil.sendAIMessageToChat(message, this.data.currentChatUser)
      
      wx.hideLoading()
      wxworkUtil.showSuccess('已发送到企业微信聊天')
    } catch (error) {
      wx.hideLoading()
      console.error('发送到企业微信聊天失败:', error)
      wxworkUtil.showError('发送失败，请重试')
    }
  },

  // 复制消息
  copyMessage(e) {
    const message = e.currentTarget.dataset.message
    wx.setClipboardData({
      data: message,
      success: () => {
        wxworkUtil.showSuccess('已复制到剪贴板')
      }
    })
  },

  // 快速问题
  askForFortune() {
    this.setData({ inputText: '请帮我分析今日运势' })
    this.sendMessage()
  },

  askForBazi() {
    this.setData({ inputText: '请根据我的生辰八字进行分析' })
    this.sendMessage()
  },

  askForFengshui() {
    this.setData({ inputText: '请给我一些风水建议' })
    this.sendMessage()
  },

  askForNameTest() {
    this.setData({ inputText: '请帮我测试姓名' })
    this.sendMessage()
  },

  askForMarriage() {
    this.setData({ inputText: '请分析我的婚姻运势' })
    this.sendMessage()
  },

  // 添加消息到列表
  addMessage(message) {
    const messages = [...this.data.messages, message]
    this.setData({
      messages,
      scrollIntoView: `msg-${message.id}`
    })
  },

  // 格式化时间
  formatTime(date) {
    const now = new Date()
    const diff = now - date
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前'
    } else if (date.toDateString() === now.toDateString()) { // 今天
      return date.toTimeString().slice(0, 5)
    } else {
      return date.toLocaleDateString()
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '卦里乾坤企业版 - AI智能助手',
      path: '/pages/wxwork-chat/wxwork-chat',
      imageUrl: '/assets/images/share-ai-chat.png'
    }
  }
}) 