<!-- 添加外层容器，设置安全区域 -->
<view class="page-container">
  <view class="chat-container">
    <!-- 聊天消息区域 -->
    <scroll-view 
      class="chat-messages" 
      scroll-y="true" 
      scroll-top="{{scrollTop}}" 
      scroll-into-view="message-{{messages.length-1}}"
      enhanced="true"
      show-scrollbar="false"
      bounces="true"
    >
      <!-- 欢迎消息 -->
      <view class="welcome-section" wx:if="{{showQuickActions}}">
        <view class="message-ai">
          <view class="message-avatar">🤖</view>
          <view class="message-content">
            <text class="message-text">欢迎使用AI助手！我可以为您提供专业的命理分析和玄学咨询。请选择下方快捷功能或直接输入您的问题。</text>
          </view>
        </view>
        
        <!-- 快捷操作 -->
        <view class="quick-actions">
          <view class="quick-item" 
                wx:for="{{quickActions}}" 
                wx:key="id" 
                data-id="{{item.id}}"
                bindtap="onQuickActionTap">
            <text class="quick-icon">{{item.icon}}</text>
            <text class="quick-text">{{item.title}}</text>
          </view>
        </view>
      </view>
      
      <!-- 消息列表 -->
      <view class="message-list">
        <view class="message-item {{item.type}}" 
              wx:for="{{messages}}" 
              wx:key="id"
              id="message-{{index}}">
          
          <!-- 消息时间 -->
          <view class="message-time-wrapper">
            <view class="message-time">{{item.timestamp}}</view>
          </view>
          
          <!-- 用户消息 -->
          <view class="message-user" wx:if="{{item.type === 'user'}}">
            <view class="message-content">
              <text class="message-text">{{item.content}}</text>
            </view>
            <view class="message-avatar">👤</view>
          </view>
          
          <!-- AI消息 -->
          <view class="message-ai" wx:if="{{item.type === 'ai' || item.type === 'system'}}">
            <view class="message-avatar">🤖</view>
            <view class="message-content">
              <text class="message-text">{{item.content}}</text>
              <view class="message-actions" wx:if="{{item.actionButton}}">
                <button class="action-btn" 
                        data-action="{{item.actionButton.action}}"
                        bindtap="onMessageAction">
                  {{item.actionButton.text}}
                </button>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- AI输入中状态 -->
      <view class="typing-indicator" wx:if="{{isTyping}}">
        <view class="message-ai">
          <view class="message-avatar">🤖</view>
          <view class="typing-content">
            <view class="typing-dots">
              <view class="dot"></view>
              <view class="dot"></view>
              <view class="dot"></view>
            </view>
            <text class="typing-text">AI正在思考...</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 悬浮输入区域 -->
    <view class="fab-input-container {{isInputExpanded ? 'expanded' : 'collapsed'}}">
      <block wx:if="{{isInputExpanded}}">
        <view class="expanded-input-area">
          <textarea class="message-input"
                    value="{{inputValue}}"
                    placeholder="说点什么..."
                    placeholder-style="color: #aaa"
                    maxlength="500"
                    cursor-spacing="20"
                    show-confirm-bar="{{false}}"
                    adjust-position="{{false}}"
                    focus="{{isInputExpanded}}"
                    bindinput="onInputChange"
                    bindconfirm="sendMessage"
                    bindfocus="onInputFocus"
                    bindblur="onInputBlur"
                    auto-height/>
          <button class="send-btn {{inputValue.trim() ? 'active' : ''}}" 
                  bindtap="sendMessage"
                  disabled="{{!inputValue.trim() || isTyping}}">
            发送
          </button>
        </view>
      </block>
      <block wx:else>
        <view class="fab-ball" bindtap="onFabClick">
          <text class="fab-icon">{{fabIcon}}</text>
        </view>
      </block>
    </view>

    <!-- 加载组件 -->
    <loading show="{{showLoading}}" text="{{loadingText}}"></loading>

    <!-- Toast组件 -->
    <toast show="{{showToast}}" message="{{toastMessage}}" type="{{toastType}}"></toast>
  </view>
  <!-- 底部安全区域 -->
  <view class="safe-bottom"></view>
</view> 