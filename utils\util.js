/**
 * 格式化时间
 * @param {Date|string|number} date 时间对象、时间字符串或时间戳
 * @param {string} format 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  const second = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

/**
 * 相对时间格式化
 * @param {Date|string|number} date 时间
 * @returns {string} 相对时间描述
 */
const formatRelativeTime = (date) => {
  if (!date) return ''
  
  const now = new Date()
  const target = new Date(date)
  const diff = now.getTime() - target.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`
  } else {
    return `${Math.floor(diff / year)}年前`
  }
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
const debounce = (func, delay) => {
  let timer = null
  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
const throttle = (func, delay) => {
  let timer = null
  return function(...args) {
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args)
        timer = null
      }, delay)
    }
  }
}

/**
 * 本地存储管理
 */
const storage = {
  /**
   * 设置本地存储
   * @param {string} key 键名
   * @param {any} value 值
   * @param {number} expire 过期时间（毫秒），不传则永不过期
   */
  set(key, value, expire) {
    const data = {
      value,
      expire: expire ? Date.now() + expire : null
    }
    wx.setStorageSync(key, JSON.stringify(data))
  },
  
  /**
   * 获取本地存储
   * @param {string} key 键名
   * @param {any} defaultValue 默认值
   * @returns {any} 存储的值
   */
  get(key, defaultValue = null) {
    try {
      const dataStr = wx.getStorageSync(key)
      if (!dataStr) return defaultValue
      
      const data = JSON.parse(dataStr)
      
      // 检查是否过期
      if (data.expire && Date.now() > data.expire) {
        wx.removeStorageSync(key)
        return defaultValue
      }
      
      return data.value
    } catch (error) {
      console.error('获取本地存储失败:', error)
      return defaultValue
    }
  },
  
  /**
   * 删除本地存储
   * @param {string} key 键名
   */
  remove(key) {
    wx.removeStorageSync(key)
  },
  
  /**
   * 清空本地存储
   */
  clear() {
    wx.clearStorageSync()
  }
}

/**
 * 分享功能
 */
const share = {
  /**
   * 分享到微信
   * @param {object} options 分享选项
   */
  toWeChat(options = {}) {
    const defaultOptions = {
      title: '卦里乾坤 - 您的命理顾问',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share.jpg'
    }
    
    return {
      ...defaultOptions,
      ...options
    }
  },
  
  /**
   * 分享帖子
   * @param {object} post 帖子信息
   */
  sharePost(post) {
    return this.toWeChat({
      title: post.title || '来看看这个有趣的帖子',
      path: `/pages/post-detail/post-detail?id=${post.id}`,
      imageUrl: post.images && post.images[0] || '/assets/images/share.jpg'
    })
  }
}

/**
 * 数字格式化
 */
const formatNumber = {
  /**
   * 格式化大数字
   * @param {number} num 数字
   * @returns {string} 格式化后的字符串
   */
  large(num) {
    if (num < 1000) return String(num)
    if (num < 10000) return (num / 1000).toFixed(1) + 'k'
    if (num < 100000000) return (num / 10000).toFixed(1) + 'w'
    return (num / 100000000).toFixed(1) + '亿'
  },
  
  /**
   * 添加千分位分隔符
   * @param {number} num 数字
   * @returns {string} 格式化后的字符串
   */
  thousands(num) {
    return String(num).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }
}

/**
 * 验证工具
 */
const validate = {
  /**
   * 验证邮箱
   * @param {string} email 邮箱地址
   * @returns {boolean} 是否有效
   */
  email(email) {
    const reg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return reg.test(email)
  },
  
  /**
   * 验证手机号
   * @param {string} phone 手机号
   * @returns {boolean} 是否有效
   */
  phone(phone) {
    const reg = /^1[3-9]\d{9}$/
    return reg.test(phone)
  },
  
  /**
   * 验证身份证号
   * @param {string} idCard 身份证号
   * @returns {boolean} 是否有效
   */
  idCard(idCard) {
    const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return reg.test(idCard)
  }
}

/**
 * 图片处理工具
 */
const image = {
  /**
   * 压缩图片
   * @param {string} filePath 图片路径
   * @param {object} options 压缩选项
   * @returns {Promise} 压缩后的图片路径
   */
  compress(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      const defaultOptions = {
        quality: 80,
        compressedWidth: 750,
        compressedHeight: 750
      }
      
      wx.compressImage({
        src: filePath,
        ...defaultOptions,
        ...options,
        success: (res) => resolve(res.tempFilePath),
        fail: reject
      })
    })
  },
  
  /**
   * 获取图片信息
   * @param {string} src 图片路径
   * @returns {Promise} 图片信息
   */
  getInfo(src) {
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src,
        success: resolve,
        fail: reject
      })
    })
  }
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 深拷贝
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  
  const cloned = {}
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key])
    }
  }
  return cloned
}

/**
 * 显示Loading
 * @param {string} title 提示文字
 */
const showLoading = (title = '加载中...') => {
  wx.showLoading({
    title,
    mask: true
  })
}

/**
 * 隐藏Loading
 */
const hideLoading = () => {
  wx.hideLoading()
}

/**
 * 显示Toast
 * @param {string} title 提示文字
 * @param {string} icon 图标类型
 * @param {number} duration 持续时间
 */
const showToast = (title, icon = 'none', duration = 2000) => {
  wx.showToast({
    title,
    icon,
    duration
  })
}

/**
 * 显示确认弹窗
 * @param {string} content 内容
 * @param {string} title 标题
 * @returns {Promise<boolean>} 用户是否确认
 */
const showConfirm = (content, title = '提示') => {
  return new Promise((resolve) => {
    wx.showModal({
      title,
      content,
      success: (res) => resolve(res.confirm),
      fail: () => resolve(false)
    })
  })
}

// 检查是否有出生信息
const checkBirthInfo = () => {
  const birthInfo = wx.getStorageSync('birthInfo');
  return birthInfo && birthInfo.name && birthInfo.birthDate && birthInfo.birthTime && birthInfo.gender;
};

// 跳转到出生信息页面
const navigateToBirthInfo = (targetPage) => {
  wx.navigateTo({
    url: '/pages/birth-info/birth-info',
    success: () => {
      // 将目标页面路径保存到本地存储
      wx.setStorageSync('targetPage', targetPage);
    }
  });
};

// 获取出生信息，如果不存在则返回默认值
const getBirthInfo = () => {
  try {
    const birthInfo = wx.getStorageSync('birthInfo') || {};
    console.log('从本地存储获取的出生信息:', birthInfo); // 添加日志
    
    return {
      name: birthInfo.name || '未设置',
      birthDate: birthInfo.birthDate || '未设置',
      birthTime: birthInfo.birthTime || '未设置',
      gender: birthInfo.gender || '男'
    };
  } catch (e) {
    console.error('获取出生信息失败:', e); // 添加错误日志
    return {
      name: '未设置',
      birthDate: '未设置',
      birthTime: '未设置',
      gender: '男'
    };
  }
};

module.exports = {
  formatTime,
  formatRelativeTime,
  debounce,
  throttle,
  storage,
  share,
  formatNumber,
  validate,
  image,
  generateId,
  deepClone,
  showLoading,
  hideLoading,
  showToast,
  showConfirm,
  checkBirthInfo,
  navigateToBirthInfo,
  getBirthInfo
}
