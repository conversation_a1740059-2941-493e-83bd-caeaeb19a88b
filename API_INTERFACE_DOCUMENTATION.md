# 卦里乾坤小程序 API接口与后台管理文档

## 📋 文档概述

本文档详细描述了"卦里乾坤"小程序的所有功能模块、数据结构、API接口规范和后台管理需求，用于指导API接口开发和后台管理界面生成。

## 🏗️ 系统架构

### 核心模块
- **用户管理系统** - 用户注册、登录、信息管理
- **出生信息管理** - 用户出生信息收集和验证
- **AI问答系统** - 智能对话和意图识别
- **命理分析系统** - 八字、易经、风水等专业分析
- **积分系统** - 用户积分管理和消费
- **企业微信集成** - 企业版功能支持

## 👤 用户管理系统

### 用户数据结构

```javascript
// 用户基础信息表 (users)
{
  "id": "string",                    // 用户唯一ID
  "openid": "string",               // 微信OpenID
  "unionid": "string",              // 微信UnionID (可选)
  "nickname": "string",             // 用户昵称
  "avatar_url": "string",           // 头像URL
  "gender": "number",               // 性别 (0:未知, 1:男, 2:女)
  "country": "string",              // 国家
  "province": "string",             // 省份
  "city": "string",                 // 城市
  "language": "string",             // 语言
  "phone": "string",                // 手机号 (可选)
  "email": "string",                // 邮箱 (可选)
  "status": "number",               // 用户状态 (0:正常, 1:禁用, 2:删除)
  "vip_level": "number",            // VIP等级 (0:普通, 1:VIP, 2:SVIP)
  "vip_expire_time": "datetime",    // VIP过期时间
  "points": "number",               // 用户积分
  "total_points": "number",         // 累计积分
  "register_time": "datetime",      // 注册时间
  "last_login_time": "datetime",    // 最后登录时间
  "login_count": "number",          // 登录次数
  "created_at": "datetime",         // 创建时间
  "updated_at": "datetime"          // 更新时间
}
```

### 用户相关API接口

#### 1. 微信登录
```
POST /api/auth/wx-login
Content-Type: application/json

Request:
{
  "code": "string",                 // 微信登录code
  "user_info": {                    // 用户信息 (可选)
    "nickname": "string",
    "avatar_url": "string",
    "gender": "number",
    "country": "string",
    "province": "string",
    "city": "string",
    "language": "string"
  }
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "user": {
      "id": "string",
      "openid": "string",
      "nickname": "string",
      "avatar_url": "string",
      "vip_level": "number",
      "points": "number"
    },
    "token": "string",              // JWT Token
    "refresh_token": "string",      // 刷新Token
    "expires_in": "number"          // Token过期时间(秒)
  }
}
```

#### 2. 获取用户信息
```
GET /api/user/profile
Authorization: Bearer {token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "user": {
      // 完整用户信息
    },
    "birth_info": {
      // 出生信息 (如果已填写)
    },
    "statistics": {
      "total_consultations": "number",  // 总咨询次数
      "total_points_earned": "number",  // 总获得积分
      "total_points_spent": "number",   // 总消费积分
      "sign_in_days": "number",         // 连续签到天数
      "last_sign_in": "date"            // 最后签到日期
    }
  }
}
```

#### 3. 更新用户信息
```
PUT /api/user/profile
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "nickname": "string",             // 昵称 (可选)
  "avatar_url": "string",           // 头像URL (可选)
  "phone": "string",                // 手机号 (可选)
  "email": "string"                 // 邮箱 (可选)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "user": {
      // 更新后的用户信息
    }
  }
}
```

## 🎂 出生信息管理

### 出生信息数据结构

```javascript
// 出生信息表 (birth_info)
{
  "id": "string",                   // 记录ID
  "user_id": "string",              // 用户ID
  "name": "string",                 // 姓名
  "gender": "string",               // 性别 (男/女)
  "birth_year": "number",           // 出生年份
  "birth_month": "number",          // 出生月份
  "birth_day": "number",            // 出生日期
  "birth_hour": "number",           // 出生小时
  "birth_minute": "number",         // 出生分钟
  "birth_timezone": "string",       // 时区 (默认: Asia/Shanghai)
  "lunar_year": "number",           // 农历年份
  "lunar_month": "number",          // 农历月份
  "lunar_day": "number",            // 农历日期
  "lunar_leap_month": "boolean",    // 是否闰月
  "zodiac": "string",               // 生肖
  "constellation": "string",        // 星座
  "lucky_number": "number",         // 幸运数字 (1-62)
  "bazi": {                         // 八字信息
    "year_gan": "string",           // 年干
    "year_zhi": "string",           // 年支
    "month_gan": "string",          // 月干
    "month_zhi": "string",          // 月支
    "day_gan": "string",            // 日干
    "day_zhi": "string",            // 日支
    "hour_gan": "string",           // 时干
    "hour_zhi": "string"            // 时支
  },
  "wuxing": {                       // 五行信息
    "jin": "number",                // 金
    "mu": "number",                 // 木
    "shui": "number",               // 水
    "huo": "number",                // 火
    "tu": "number"                  // 土
  },
  "is_verified": "boolean",         // 是否已验证
  "created_at": "datetime",         // 创建时间
  "updated_at": "datetime"          // 更新时间
}
```

### 出生信息API接口

#### 1. 保存出生信息
```
POST /api/birth-info
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": "string",                 // 姓名 (必填)
  "gender": "string",               // 性别 (必填: 男/女)
  "birth_year": "number",           // 出生年份 (必填)
  "birth_month": "number",          // 出生月份 (必填)
  "birth_day": "number",            // 出生日期 (必填)
  "birth_hour": "number",           // 出生小时 (必填)
  "birth_minute": "number",         // 出生分钟 (可选, 默认0)
  "zodiac": "string",               // 生肖 (可选)
  "lucky_number": "number"          // 幸运数字 (可选)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "birth_info": {
      // 完整出生信息，包含计算后的八字、五行等
    },
    "analysis": {
      "bazi_summary": "string",     // 八字简要分析
      "wuxing_summary": "string",   // 五行简要分析
      "personality": "string",      // 性格特点
      "fortune_trend": "string"     // 运势趋势
    }
  }
}
```

#### 2. 获取出生信息
```
GET /api/birth-info
Authorization: Bearer {token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "birth_info": {
      // 完整出生信息
    }
  }
}
```

#### 3. 更新出生信息
```
PUT /api/birth-info
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  // 与保存接口相同的字段结构
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "birth_info": {
      // 更新后的完整出生信息
    }
  }
}
```

## 🤖 AI问答系统

### AI问答数据结构

```javascript
// 聊天记录表 (chat_messages)
{
  "id": "string",                   // 消息ID
  "user_id": "string",              // 用户ID
  "session_id": "string",           // 会话ID
  "message_type": "string",         // 消息类型 (user/ai/system)
  "content": "text",                // 消息内容
  "intent": "string",               // 意图识别结果
  "context": "json",                // 上下文信息
  "attachments": "json",            // 附件信息
  "metadata": {                     // 元数据
    "analysis_type": "string",      // 分析类型
    "confidence": "number",         // 置信度
    "processing_time": "number",    // 处理时间(ms)
    "model_version": "string",      // 模型版本
    "tokens_used": "number",        // 使用的token数
    "api_cost": "number"            // API调用成本
  },
  "feedback": {                     // 用户反馈
    "rating": "number",             // 评分 (1-5)
    "feedback_type": "string",      // 反馈类型
    "comment": "string",            // 评论
    "tags": ["string"]              // 标签
  },
  "is_deleted": "boolean",          // 是否删除
  "created_at": "datetime",         // 创建时间
  "updated_at": "datetime"          // 更新时间
}

// 会话表 (chat_sessions)
{
  "id": "string",                   // 会话ID
  "user_id": "string",              // 用户ID
  "title": "string",                // 会话标题
  "status": "string",               // 状态 (active/ended/archived)
  "message_count": "number",        // 消息数量
  "total_tokens": "number",         // 总token消耗
  "total_cost": "number",           // 总成本
  "start_time": "datetime",         // 开始时间
  "end_time": "datetime",           // 结束时间
  "last_activity": "datetime",      // 最后活动时间
  "metadata": {                     // 会话元数据
    "user_agent": "string",         // 用户代理
    "ip_address": "string",         // IP地址
    "platform": "string",           // 平台
    "initial_intent": "string"      // 初始意图
  },
  "created_at": "datetime",
  "updated_at": "datetime"
}

// 意图配置表 (intent_configs)
{
  "id": "string",                   // 配置ID
  "intent_name": "string",          // 意图名称
  "keywords": ["string"],           // 关键词列表
  "patterns": ["string"],           // 正则模式
  "confidence_threshold": "number", // 置信度阈值
  "response_templates": ["string"], // 回复模板
  "actions": ["object"],            // 关联操作
  "requires_birth_info": "boolean", // 是否需要出生信息
  "points_cost": "number",          // 积分消费
  "is_active": "boolean",           // 是否启用
  "priority": "number",             // 优先级
  "created_at": "datetime",
  "updated_at": "datetime"
}

// AI配置表 (ai_configs)
{
  "id": "string",                   // 配置ID
  "config_type": "string",          // 配置类型 (model/chat/intent)
  "config_name": "string",          // 配置名称
  "config_data": "json",            // 配置数据
  "version": "string",              // 版本
  "is_active": "boolean",           // 是否启用
  "effective_time": "datetime",     // 生效时间
  "created_by": "string",           // 创建者
  "created_at": "datetime",
  "updated_at": "datetime"
}

// 知识库文档表 (knowledge_documents)
{
  "id": "string",                   // 文档ID
  "title": "string",                // 文档标题
  "content": "text",                // 文档内容
  "summary": "text",                // 文档摘要
  "category": "string",             // 分类 (bazi/yijing/fengshui/wuxing/ziwei/general)
  "tags": ["string"],               // 标签列表
  "keywords": ["string"],           // 关键词
  "difficulty_level": "string",     // 难度等级 (beginner/intermediate/advanced)
  "content_type": "string",         // 内容类型 (text/markdown/html)
  "source": "string",               // 来源
  "author": "string",               // 作者
  "version": "string",              // 版本
  "status": "string",               // 状态 (draft/published/archived)
  "view_count": "number",           // 查看次数
  "like_count": "number",           // 点赞次数
  "embedding_vector": "json",       // 向量嵌入
  "last_updated_by": "string",      // 最后更新者
  "published_at": "datetime",       // 发布时间
  "created_at": "datetime",
  "updated_at": "datetime"
}

// 知识库问答对表 (knowledge_qa_pairs)
{
  "id": "string",                   // 问答对ID
  "question": "text",               // 问题
  "answer": "text",                 // 答案
  "category": "string",             // 分类
  "tags": ["string"],               // 标签
  "difficulty": "string",           // 难度
  "confidence_score": "number",     // 置信度评分
  "usage_count": "number",          // 使用次数
  "success_rate": "number",         // 成功率
  "related_documents": ["string"],  // 关联文档ID
  "embedding_vector": "json",       // 向量嵌入
  "is_active": "boolean",           // 是否启用
  "created_by": "string",           // 创建者
  "reviewed_by": "string",          // 审核者
  "reviewed_at": "datetime",        // 审核时间
  "created_at": "datetime",
  "updated_at": "datetime"
}

// 知识库搜索日志表 (knowledge_search_logs)
{
  "id": "string",                   // 日志ID
  "user_id": "string",              // 用户ID
  "session_id": "string",           // 会话ID
  "query": "text",                  // 搜索查询
  "search_type": "string",          // 搜索类型 (semantic/keyword/hybrid)
  "results_count": "number",        // 结果数量
  "top_result_id": "string",        // 最佳结果ID
  "click_through": "boolean",       // 是否点击
  "user_feedback": "string",        // 用户反馈 (helpful/not_helpful/irrelevant)
  "response_time": "number",        // 响应时间(ms)
  "search_filters": "json",         // 搜索过滤器
  "created_at": "datetime"
}
```

### AI问答API接口

#### 1. 发送消息
```
POST /api/ai-chat/message
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "message": "string",              // 用户消息 (必填)
  "session_id": "string",           // 会话ID (可选，新会话时不传)
  "context": [                      // 上下文消息 (可选)
    {
      "role": "user|assistant",
      "content": "string",
      "timestamp": "datetime"
    }
  ],
  "intent_hint": "string",          // 意图提示 (可选)
  "analysis_type": "string"         // 分析类型 (可选: bazi/yijing/fengshui/wuxing)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "session_id": "string",         // 会话ID
    "message_id": "string",         // 消息ID
    "response": {
      "content": "string",          // AI回复内容
      "type": "string",             // 回复类型 (text/analysis/suggestion)
      "intent": "string",           // 识别的意图
      "confidence": "number",       // 置信度 (0-1)
      "suggestions": ["string"],    // 建议问题
      "actions": [                  // 可执行操作
        {
          "type": "string",         // 操作类型
          "text": "string",         // 显示文本
          "url": "string",          // 跳转链接
          "icon": "string"          // 图标
        }
      ]
    },
    "analysis": {                   // 分析结果 (如果有)
      "type": "string",             // 分析类型
      "result": "object",           // 分析结果
      "score": "number",            // 评分
      "summary": "string"           // 总结
    },
    "usage": {                      // 使用统计
      "tokens_used": "number",      // 使用的token数
      "points_cost": "number"       // 消耗积分
    }
  }
}
```

#### 2. 获取聊天历史
```
GET /api/ai-chat/history
Authorization: Bearer {token}
Query Parameters:
- session_id: string (可选，特定会话)
- page: number (页码，默认1)
- limit: number (每页数量，默认20)
- start_date: date (开始日期，可选)
- end_date: date (结束日期，可选)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "messages": [
      {
        "id": "string",
        "session_id": "string",
        "message_type": "string",
        "content": "string",
        "intent": "string",
        "created_at": "datetime"
      }
    ],
    "pagination": {
      "current_page": "number",
      "total_pages": "number",
      "total_count": "number",
      "has_next": "boolean",
      "has_prev": "boolean"
    }
  }
}
```

#### 3. 删除聊天记录
```
DELETE /api/ai-chat/session/{session_id}
Authorization: Bearer {token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "deleted_count": "number"
  }
}
```

#### 4. 快捷操作询问
```
POST /api/ai-chat/quick-action
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "action_id": "string",            // 快捷操作ID (bazi/yijing/fengshui/wuxing)
  "session_id": "string",           // 会话ID (可选)
  "additional_info": "string"       // 额外信息 (可选)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "session_id": "string",
    "message_id": "string",
    "response": {
      "content": "string",          // AI回复内容
      "type": "analysis_guide",     // 回复类型
      "analysis_type": "string",    // 分析类型
      "requires_birth_info": "boolean", // 是否需要出生信息
      "actions": [                  // 可执行操作
        {
          "type": "navigate",       // 操作类型
          "text": "完善出生信息",    // 显示文本
          "url": "/pages/birth-info/birth-info", // 跳转链接
          "icon": "📝"              // 图标
        },
        {
          "type": "analysis",
          "text": "开始分析",
          "url": "/pages/bazi/bazi",
          "icon": "🔮"
        }
      ]
    },
    "birth_info_status": {          // 出生信息状态
      "has_birth_info": "boolean",
      "missing_fields": ["string"], // 缺失字段
      "completeness": "number"      // 完整度百分比
    }
  }
}
```

#### 5. 意图识别
```
POST /api/ai-chat/intent-recognition
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "text": "string",                   // 用户输入文本
  "context": ["object"],           // 上下文 (可选)
  "user_profile": "object"         // 用户画像 (可选)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "intent": {
      "primary": "string",          // 主要意图
      "secondary": ["string"],      // 次要意图
      "confidence": "number",       // 置信度 (0-1)
      "entities": [                 // 实体识别
        {
          "entity": "string",       // 实体名称
          "value": "string",        // 实体值
          "confidence": "number"    // 置信度
        }
      ]
    },
    "suggested_actions": [          // 建议操作
      {
        "action": "string",
        "description": "string",
        "priority": "number"
      }
    ],
    "response_template": "string",  // 回复模板
    "requires_clarification": "boolean" // 是否需要澄清
  }
}
```

#### 6. 获取AI配置
```
GET /api/ai-chat/config
Authorization: Bearer {token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "model_config": {
      "model_name": "string",       // 模型名称
      "version": "string",          // 版本
      "max_tokens": "number",       // 最大token数
      "temperature": "number",      // 温度参数
      "top_p": "number",           // top_p参数
      "frequency_penalty": "number", // 频率惩罚
      "presence_penalty": "number"  // 存在惩罚
    },
    "chat_config": {
      "max_history": "number",      // 最大历史记录数
      "session_timeout": "number",  // 会话超时时间(分钟)
      "typing_delay": "number",     // 打字延迟(毫秒)
      "max_message_length": "number" // 最大消息长度
    },
    "intent_config": {
      "confidence_threshold": "number", // 置信度阈值
      "supported_intents": ["string"],  // 支持的意图列表
      "fallback_responses": ["string"]  // 兜底回复
    },
    "quick_actions": [              // 快捷操作配置
      {
        "id": "string",
        "title": "string",
        "icon": "string",
        "description": "string",
        "requires_birth_info": "boolean",
        "points_cost": "number"
      }
    ]
  }
}
```

#### 7. 更新AI配置 (管理员)
```
PUT /api/ai-chat/config
Authorization: Bearer {admin_token}
Content-Type: application/json

Request:
{
  "config_type": "string",          // 配置类型 (model/chat/intent/quick_actions)
  "config_data": "object"           // 配置数据
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "updated_config": "object",
    "effective_time": "datetime"     // 生效时间
  }
}
```

#### 8. 获取会话统计
```
GET /api/ai-chat/sessions/stats
Authorization: Bearer {token}
Query Parameters:
- period: string (统计周期: today/week/month)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "session_stats": {
      "total_sessions": "number",   // 总会话数
      "active_sessions": "number",  // 活跃会话数
      "avg_messages_per_session": "number", // 平均消息数
      "avg_session_duration": "number" // 平均会话时长(分钟)
    },
    "message_stats": {
      "total_messages": "number",   // 总消息数
      "user_messages": "number",    // 用户消息数
      "ai_messages": "number",      // AI消息数
      "avg_response_time": "number" // 平均响应时间(毫秒)
    },
    "intent_stats": [               // 意图统计
      {
        "intent": "string",
        "count": "number",
        "percentage": "number"
      }
    ],
    "satisfaction_stats": {         // 满意度统计
      "total_feedbacks": "number",
      "positive_rate": "number",    // 好评率
      "negative_rate": "number",    // 差评率
      "avg_rating": "number"        // 平均评分
    }
  }
}
```

#### 9. 消息反馈
```
POST /api/ai-chat/feedback
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "message_id": "string",           // 消息ID
  "feedback_type": "string",        // 反馈类型 (like/dislike/report)
  "rating": "number",               // 评分 (1-5)
  "comment": "string",              // 评论 (可选)
  "tags": ["string"]                // 标签 (可选)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "feedback_id": "string",
    "points_reward": "number"       // 反馈奖励积分
  }
}
```

#### 10. 导出聊天记录
```
POST /api/ai-chat/export
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "session_ids": ["string"],        // 会话ID列表 (可选，不传则导出所有)
  "start_date": "date",             // 开始日期 (可选)
  "end_date": "date",               // 结束日期 (可选)
  "format": "string",               // 导出格式 (json/csv/pdf)
  "include_analysis": "boolean"     // 是否包含分析结果
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "export_id": "string",          // 导出任务ID
    "download_url": "string",       // 下载链接 (异步生成)
    "estimated_time": "number",     // 预估完成时间(秒)
    "file_size": "number"           // 预估文件大小(字节)
  }
}
```

#### 11. 获取导出状态
```
GET /api/ai-chat/export/{export_id}/status
Authorization: Bearer {token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "export_status": "string",      // 导出状态 (pending/processing/completed/failed)
    "progress": "number",           // 进度百分比
    "download_url": "string",       // 下载链接 (完成后可用)
    "file_size": "number",          // 文件大小
    "expires_at": "datetime"        // 下载链接过期时间
  }
}
```

#### 12. AI问答管理接口 (管理员)

##### 12.1 获取所有会话 (管理员)
```
GET /api/admin/ai-chat/sessions
Authorization: Bearer {admin_token}
Query Parameters:
- page: number (页码)
- limit: number (每页数量)
- user_id: string (用户ID筛选)
- status: string (状态筛选)
- start_date: date (开始日期)
- end_date: date (结束日期)
- intent: string (意图筛选)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "sessions": [
      {
        "id": "string",
        "user_id": "string",
        "user_nickname": "string",
        "title": "string",
        "status": "string",
        "message_count": "number",
        "total_tokens": "number",
        "total_cost": "number",
        "start_time": "datetime",
        "end_time": "datetime",
        "initial_intent": "string"
      }
    ],
    "pagination": {
      "current_page": "number",
      "total_pages": "number",
      "total_count": "number"
    },
    "statistics": {
      "total_sessions": "number",
      "active_sessions": "number",
      "total_messages": "number",
      "total_tokens": "number",
      "total_cost": "number",
      "avg_session_duration": "number"
    }
  }
}
```

##### 12.2 获取意图配置 (管理员)
```
GET /api/admin/ai-chat/intents
Authorization: Bearer {admin_token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "intents": [
      {
        "id": "string",
        "intent_name": "string",
        "keywords": ["string"],
        "patterns": ["string"],
        "confidence_threshold": "number",
        "response_templates": ["string"],
        "requires_birth_info": "boolean",
        "points_cost": "number",
        "is_active": "boolean",
        "priority": "number",
        "usage_count": "number",        // 使用次数
        "success_rate": "number"        // 成功率
      }
    ]
  }
}
```

##### 12.3 创建/更新意图配置 (管理员)
```
POST /api/admin/ai-chat/intents
PUT /api/admin/ai-chat/intents/{intent_id}
Authorization: Bearer {admin_token}
Content-Type: application/json

Request:
{
  "intent_name": "string",          // 意图名称
  "keywords": ["string"],           // 关键词列表
  "patterns": ["string"],           // 正则模式
  "confidence_threshold": "number", // 置信度阈值
  "response_templates": ["string"], // 回复模板
  "actions": [                      // 关联操作
    {
      "type": "string",             // 操作类型
      "config": "object"            // 操作配置
    }
  ],
  "requires_birth_info": "boolean",
  "points_cost": "number",
  "is_active": "boolean",
  "priority": "number"
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "intent": {
      // 完整意图配置
    }
  }
}
```

##### 12.4 获取AI模型配置 (管理员)
```
GET /api/admin/ai-chat/models
Authorization: Bearer {admin_token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "current_model": {
      "model_name": "string",
      "version": "string",
      "provider": "string",          // 提供商 (openai/anthropic/local)
      "api_endpoint": "string",
      "max_tokens": "number",
      "temperature": "number",
      "top_p": "number",
      "frequency_penalty": "number",
      "presence_penalty": "number"
    },
    "available_models": [            // 可用模型列表
      {
        "model_name": "string",
        "version": "string",
        "provider": "string",
        "capabilities": ["string"],   // 能力列表
        "cost_per_token": "number",   // 每token成本
        "max_context": "number"       // 最大上下文长度
      }
    ],
    "usage_statistics": {            // 使用统计
      "total_requests": "number",
      "total_tokens": "number",
      "total_cost": "number",
      "avg_response_time": "number",
      "error_rate": "number"
    }
  }
}
```

##### 12.5 更新AI模型配置 (管理员)
```
PUT /api/admin/ai-chat/models/config
Authorization: Bearer {admin_token}
Content-Type: application/json

Request:
{
  "model_name": "string",
  "version": "string",
  "provider": "string",
  "api_endpoint": "string",
  "api_key": "string",
  "max_tokens": "number",
  "temperature": "number",
  "top_p": "number",
  "frequency_penalty": "number",
  "presence_penalty": "number",
  "timeout": "number",              // 超时时间(秒)
  "retry_attempts": "number",       // 重试次数
  "rate_limit": "number"            // 速率限制(请求/分钟)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "config": {
      // 更新后的配置
    },
    "test_result": {                // 配置测试结果
      "success": "boolean",
      "response_time": "number",
      "error_message": "string"
    }
  }
}
```

##### 12.6 获取聊天质量分析 (管理员)
```
GET /api/admin/ai-chat/quality-analysis
Authorization: Bearer {admin_token}
Query Parameters:
- period: string (分析周期: today/week/month/quarter)
- intent: string (意图筛选)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "overall_metrics": {
      "total_conversations": "number",
      "avg_satisfaction": "number",   // 平均满意度
      "resolution_rate": "number",    // 问题解决率
      "avg_response_time": "number",  // 平均响应时间
      "user_retention_rate": "number" // 用户留存率
    },
    "intent_performance": [          // 意图表现
      {
        "intent": "string",
        "usage_count": "number",
        "success_rate": "number",
        "avg_confidence": "number",
        "user_satisfaction": "number"
      }
    ],
    "common_issues": [               // 常见问题
      {
        "issue_type": "string",
        "frequency": "number",
        "description": "string",
        "suggested_solution": "string"
      }
    ],
    "improvement_suggestions": [     // 改进建议
      {
        "area": "string",            // 改进领域
        "priority": "string",        // 优先级
        "description": "string",
        "expected_impact": "string"
      }
    ]
  }
}
```

##### 12.7 批量操作聊天记录 (管理员)
```
POST /api/admin/ai-chat/batch-operations
Authorization: Bearer {admin_token}
Content-Type: application/json

Request:
{
  "operation": "string",            // 操作类型 (delete/archive/export/analyze)
  "filters": {                      // 筛选条件
    "user_ids": ["string"],
    "session_ids": ["string"],
    "start_date": "date",
    "end_date": "date",
    "intent": "string",
    "rating_range": [1, 5]
  },
  "options": {                      // 操作选项
    "export_format": "string",      // 导出格式 (仅导出操作)
    "include_user_info": "boolean", // 是否包含用户信息
    "anonymize": "boolean"          // 是否匿名化
  }
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "operation_id": "string",       // 操作ID
    "affected_count": "number",     // 影响的记录数
    "status": "string",             // 操作状态
    "download_url": "string",       // 下载链接 (仅导出操作)
    "estimated_completion": "datetime" // 预计完成时间
  }
}
```

## 📚 AI知识库系统

### 知识库API接口

#### 1. 知识库搜索
```
POST /api/knowledge/search
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "query": "string",                // 搜索查询 (必填)
  "search_type": "string",          // 搜索类型 (semantic/keyword/hybrid，默认hybrid)
  "category": "string",             // 分类筛选 (可选)
  "tags": ["string"],               // 标签筛选 (可选)
  "difficulty": "string",           // 难度筛选 (可选)
  "limit": "number",                // 返回数量 (默认10，最大50)
  "include_content": "boolean",     // 是否包含完整内容 (默认false)
  "user_context": {                 // 用户上下文 (可选)
    "current_analysis": "string",   // 当前分析类型
    "user_level": "string",         // 用户等级
    "preferences": ["string"]       // 用户偏好
  }
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "search_id": "string",          // 搜索ID
    "query": "string",              // 原始查询
    "total_results": "number",      // 总结果数
    "search_time": "number",        // 搜索耗时(ms)
    "results": [
      {
        "id": "string",             // 文档/问答对ID
        "type": "string",           // 类型 (document/qa_pair)
        "title": "string",          // 标题
        "summary": "string",        // 摘要
        "content": "string",        // 内容 (如果include_content=true)
        "category": "string",       // 分类
        "tags": ["string"],         // 标签
        "relevance_score": "number", // 相关性评分 (0-1)
        "confidence": "number",     // 置信度 (0-1)
        "source": "string",         // 来源
        "last_updated": "datetime", // 最后更新时间
        "view_count": "number",     // 查看次数
        "helpful_count": "number"   // 有用评价数
      }
    ],
    "related_queries": ["string"],  // 相关查询建议
    "search_suggestions": ["string"] // 搜索建议
  }
}
```

#### 2. 获取知识库文档详情
```
GET /api/knowledge/documents/{document_id}
Authorization: Bearer {token}
Query Parameters:
- track_view: boolean (是否记录查看，默认true)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "document": {
      "id": "string",
      "title": "string",
      "content": "string",
      "summary": "string",
      "category": "string",
      "tags": ["string"],
      "keywords": ["string"],
      "difficulty_level": "string",
      "content_type": "string",
      "source": "string",
      "author": "string",
      "version": "string",
      "view_count": "number",
      "like_count": "number",
      "published_at": "datetime",
      "updated_at": "datetime"
    },
    "related_documents": [          // 相关文档
      {
        "id": "string",
        "title": "string",
        "summary": "string",
        "relevance_score": "number"
      }
    ],
    "user_interaction": {           // 用户交互状态
      "has_liked": "boolean",       // 是否已点赞
      "has_bookmarked": "boolean",  // 是否已收藏
      "reading_progress": "number"  // 阅读进度 (0-100)
    }
  }
}
```

#### 3. 获取问答对详情
```
GET /api/knowledge/qa-pairs/{qa_id}
Authorization: Bearer {token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "qa_pair": {
      "id": "string",
      "question": "string",
      "answer": "string",
      "category": "string",
      "tags": ["string"],
      "difficulty": "string",
      "confidence_score": "number",
      "usage_count": "number",
      "success_rate": "number",
      "created_at": "datetime",
      "updated_at": "datetime"
    },
    "related_documents": [          // 关联文档
      {
        "id": "string",
        "title": "string",
        "summary": "string"
      }
    ],
    "similar_questions": [          // 相似问题
      {
        "id": "string",
        "question": "string",
        "similarity_score": "number"
      }
    ]
  }
}
```

#### 4. 知识库分类浏览
```
GET /api/knowledge/categories
Authorization: Bearer {token}
Query Parameters:
- include_stats: boolean (是否包含统计信息，默认false)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "categories": [
      {
        "name": "string",           // 分类名称
        "display_name": "string",   // 显示名称
        "description": "string",    // 描述
        "icon": "string",           // 图标
        "document_count": "number", // 文档数量
        "qa_count": "number",       // 问答对数量
        "subcategories": [          // 子分类
          {
            "name": "string",
            "display_name": "string",
            "document_count": "number"
          }
        ]
      }
    ]
  }
}
```

#### 5. 获取分类下的内容
```
GET /api/knowledge/categories/{category}/content
Authorization: Bearer {token}
Query Parameters:
- type: string (内容类型: documents/qa_pairs/all，默认all)
- page: number (页码，默认1)
- limit: number (每页数量，默认20)
- sort: string (排序: relevance/date/popularity，默认relevance)
- difficulty: string (难度筛选)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "category": {
      "name": "string",
      "display_name": "string",
      "description": "string"
    },
    "content": [
      {
        "id": "string",
        "type": "string",           // document/qa_pair
        "title": "string",
        "summary": "string",
        "tags": ["string"],
        "difficulty": "string",
        "popularity_score": "number",
        "last_updated": "datetime"
      }
    ],
    "pagination": {
      "current_page": "number",
      "total_pages": "number",
      "total_count": "number"
    },
    "filters": {
      "available_tags": ["string"],
      "difficulty_levels": ["string"]
    }
  }
}
```

#### 6. 用户反馈
```
POST /api/knowledge/feedback
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "content_id": "string",           // 内容ID (文档或问答对)
  "content_type": "string",         // 内容类型 (document/qa_pair)
  "feedback_type": "string",        // 反馈类型 (helpful/not_helpful/incorrect/outdated)
  "rating": "number",               // 评分 (1-5)
  "comment": "string",              // 评论 (可选)
  "search_query": "string",         // 相关搜索查询 (可选)
  "context": "string"               // 使用场景 (可选)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "feedback_id": "string",
    "points_reward": "number"       // 反馈奖励积分
  }
}
```

#### 7. 收藏管理
```
POST /api/knowledge/bookmarks
DELETE /api/knowledge/bookmarks/{content_id}
GET /api/knowledge/bookmarks
Authorization: Bearer {token}

# 添加收藏
POST Request:
{
  "content_id": "string",
  "content_type": "string",         // document/qa_pair
  "notes": "string"                 // 个人笔记 (可选)
}

# 获取收藏列表
GET Response:
{
  "status": "success|error",
  "data": {
    "bookmarks": [
      {
        "id": "string",
        "content_id": "string",
        "content_type": "string",
        "title": "string",
        "summary": "string",
        "notes": "string",
        "bookmarked_at": "datetime"
      }
    ]
  }
}
```

#### 8. 智能推荐
```
GET /api/knowledge/recommendations
Authorization: Bearer {token}
Query Parameters:
- context: string (推荐上下文: chat/analysis/browse)
- limit: number (推荐数量，默认10)
- user_level: string (用户等级)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "recommendations": [
      {
        "id": "string",
        "type": "string",
        "title": "string",
        "summary": "string",
        "reason": "string",           // 推荐理由
        "relevance_score": "number",  // 相关性评分
        "category": "string",
        "tags": ["string"]
      }
    ],
    "recommendation_context": {
      "based_on": ["string"],         // 推荐依据
      "user_interests": ["string"],   // 用户兴趣
      "trending_topics": ["string"]   // 热门话题
    }
  }
}
```

### 知识库管理接口 (管理员)

#### 9. 文档管理 (管理员)

##### 9.1 获取所有文档
```
GET /api/admin/knowledge/documents
Authorization: Bearer {admin_token}
Query Parameters:
- page: number (页码)
- limit: number (每页数量)
- category: string (分类筛选)
- status: string (状态筛选)
- search: string (搜索关键词)
- sort: string (排序字段)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "documents": [
      {
        "id": "string",
        "title": "string",
        "summary": "string",
        "category": "string",
        "status": "string",
        "author": "string",
        "view_count": "number",
        "like_count": "number",
        "last_updated_by": "string",
        "published_at": "datetime",
        "updated_at": "datetime"
      }
    ],
    "pagination": {
      "current_page": "number",
      "total_pages": "number",
      "total_count": "number"
    },
    "statistics": {
      "total_documents": "number",
      "published_documents": "number",
      "draft_documents": "number",
      "total_views": "number"
    }
  }
}
```

##### 9.2 创建/更新文档
```
POST /api/admin/knowledge/documents
PUT /api/admin/knowledge/documents/{document_id}
Authorization: Bearer {admin_token}
Content-Type: application/json

Request:
{
  "title": "string",                // 文档标题 (必填)
  "content": "string",              // 文档内容 (必填)
  "summary": "string",              // 文档摘要 (可选)
  "category": "string",             // 分类 (必填)
  "tags": ["string"],               // 标签 (可选)
  "keywords": ["string"],           // 关键词 (可选)
  "difficulty_level": "string",     // 难度等级 (可选)
  "content_type": "string",         // 内容类型 (默认markdown)
  "source": "string",               // 来源 (可选)
  "author": "string",               // 作者 (可选)
  "status": "string",               // 状态 (draft/published，默认draft)
  "auto_generate_embedding": "boolean" // 是否自动生成向量嵌入 (默认true)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "document": {
      // 完整文档信息
    },
    "embedding_status": "string",   // 向量嵌入状态
    "related_content": [            // 自动识别的相关内容
      {
        "id": "string",
        "title": "string",
        "similarity_score": "number"
      }
    ]
  }
}
```

##### 9.3 批量操作文档
```
POST /api/admin/knowledge/documents/batch
Authorization: Bearer {admin_token}
Content-Type: application/json

Request:
{
  "operation": "string",            // 操作类型 (publish/unpublish/delete/update_category)
  "document_ids": ["string"],       // 文档ID列表
  "parameters": {                   // 操作参数
    "new_category": "string",       // 新分类 (update_category操作)
    "new_status": "string"          // 新状态 (状态更新操作)
  }
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "operation_id": "string",
    "affected_count": "number",
    "success_count": "number",
    "failed_count": "number",
    "failed_items": [
      {
        "document_id": "string",
        "error_reason": "string"
      }
    ]
  }
}
```

#### 10. 问答对管理 (管理员)

##### 10.1 获取所有问答对
```
GET /api/admin/knowledge/qa-pairs
Authorization: Bearer {admin_token}
Query Parameters:
- page: number
- limit: number
- category: string
- is_active: boolean
- search: string

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "qa_pairs": [
      {
        "id": "string",
        "question": "string",
        "answer": "string",
        "category": "string",
        "difficulty": "string",
        "confidence_score": "number",
        "usage_count": "number",
        "success_rate": "number",
        "is_active": "boolean",
        "created_by": "string",
        "reviewed_by": "string",
        "created_at": "datetime",
        "updated_at": "datetime"
      }
    ],
    "pagination": {
      "current_page": "number",
      "total_pages": "number",
      "total_count": "number"
    }
  }
}
```

##### 10.2 创建/更新问答对
```
POST /api/admin/knowledge/qa-pairs
PUT /api/admin/knowledge/qa-pairs/{qa_id}
Authorization: Bearer {admin_token}
Content-Type: application/json

Request:
{
  "question": "string",             // 问题 (必填)
  "answer": "string",               // 答案 (必填)
  "category": "string",             // 分类 (必填)
  "tags": ["string"],               // 标签 (可选)
  "difficulty": "string",           // 难度 (可选)
  "confidence_score": "number",     // 置信度评分 (可选)
  "related_documents": ["string"],  // 关联文档ID (可选)
  "is_active": "boolean",           // 是否启用 (默认true)
  "auto_generate_embedding": "boolean" // 是否自动生成向量嵌入
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "qa_pair": {
      // 完整问答对信息
    },
    "similar_questions": [          // 相似问题检测
      {
        "id": "string",
        "question": "string",
        "similarity_score": "number"
      }
    ]
  }
}
```

#### 11. 知识库统计分析 (管理员)

##### 11.1 获取知识库统计
```
GET /api/admin/knowledge/statistics
Authorization: Bearer {admin_token}
Query Parameters:
- period: string (统计周期: today/week/month/quarter)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "content_stats": {
      "total_documents": "number",
      "total_qa_pairs": "number",
      "published_documents": "number",
      "active_qa_pairs": "number",
      "total_categories": "number"
    },
    "usage_stats": {
      "total_searches": "number",
      "unique_searchers": "number",
      "avg_search_results": "number",
      "popular_queries": [
        {
          "query": "string",
          "count": "number"
        }
      ]
    },
    "content_performance": [
      {
        "content_id": "string",
        "title": "string",
        "type": "string",
        "view_count": "number",
        "helpful_rate": "number",
        "search_rank": "number"
      }
    ],
    "category_distribution": [
      {
        "category": "string",
        "document_count": "number",
        "qa_count": "number",
        "usage_percentage": "number"
      }
    ],
    "user_feedback_summary": {
      "total_feedbacks": "number",
      "positive_rate": "number",
      "avg_rating": "number",
      "common_issues": ["string"]
    }
  }
}
```

##### 11.2 搜索质量分析
```
GET /api/admin/knowledge/search-quality
Authorization: Bearer {admin_token}
Query Parameters:
- period: string
- category: string

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "search_metrics": {
      "total_searches": "number",
      "avg_response_time": "number",
      "zero_result_rate": "number",
      "click_through_rate": "number",
      "user_satisfaction": "number"
    },
    "query_analysis": {
      "top_queries": [
        {
          "query": "string",
          "count": "number",
          "avg_results": "number",
          "satisfaction_rate": "number"
        }
      ],
      "failed_queries": [
        {
          "query": "string",
          "count": "number",
          "failure_reason": "string"
        }
      ],
      "trending_topics": ["string"]
    },
    "content_gaps": [
      {
        "topic": "string",
        "search_frequency": "number",
        "content_availability": "string",
        "priority": "string"
      }
    ],
    "improvement_suggestions": [
      {
        "area": "string",
        "description": "string",
        "impact": "string",
        "effort": "string"
      }
    ]
  }
}
```

#### 12. 向量嵌入管理 (管理员)

##### 12.1 重新生成向量嵌入
```
POST /api/admin/knowledge/embeddings/regenerate
Authorization: Bearer {admin_token}
Content-Type: application/json

Request:
{
  "content_type": "string",         // 内容类型 (documents/qa_pairs/all)
  "content_ids": ["string"],        // 特定内容ID (可选)
  "force_update": "boolean",        // 是否强制更新 (默认false)
  "batch_size": "number"            // 批处理大小 (默认100)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "task_id": "string",            // 任务ID
    "total_items": "number",        // 总项目数
    "estimated_time": "number",     // 预估时间(分钟)
    "status": "string"              // 任务状态
  }
}
```

##### 12.2 获取向量嵌入任务状态
```
GET /api/admin/knowledge/embeddings/tasks/{task_id}
Authorization: Bearer {admin_token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "task_id": "string",
    "status": "string",             // pending/running/completed/failed
    "progress": "number",           // 进度百分比
    "processed_items": "number",    // 已处理项目数
    "total_items": "number",        // 总项目数
    "failed_items": "number",       // 失败项目数
    "start_time": "datetime",
    "end_time": "datetime",
    "error_details": [
      {
        "content_id": "string",
        "error_message": "string"
      }
    ]
  }
}
```

## 🔮 命理分析系统

### 分析结果数据结构

```javascript
// 分析记录表 (analysis_records)
{
  "id": "string",                   // 记录ID
  "user_id": "string",              // 用户ID
  "analysis_type": "string",        // 分析类型 (bazi/yijing/fengshui/wuxing/ziwei/marriage)
  "input_data": "json",             // 输入数据
  "result_data": "json",            // 分析结果
  "score": "number",                // 评分 (0-100)
  "summary": "text",                // 总结
  "suggestions": "json",            // 建议
  "points_cost": "number",          // 消耗积分
  "processing_time": "number",      // 处理时间(ms)
  "is_shared": "boolean",           // 是否分享
  "share_count": "number",          // 分享次数
  "created_at": "datetime",         // 创建时间
  "updated_at": "datetime"          // 更新时间
}
```

### 命理分析API接口

#### 1. 八字分析
```
POST /api/analysis/bazi
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "use_birth_info": "boolean",      // 是否使用已保存的出生信息 (默认true)
  "birth_info": {                   // 临时出生信息 (use_birth_info为false时必填)
    "name": "string",
    "gender": "string",
    "birth_year": "number",
    "birth_month": "number",
    "birth_day": "number",
    "birth_hour": "number",
    "birth_minute": "number"
  },
  "analysis_options": {             // 分析选项
    "include_fortune": "boolean",   // 是否包含运势分析
    "include_career": "boolean",    // 是否包含事业分析
    "include_marriage": "boolean",  // 是否包含婚姻分析
    "include_health": "boolean",    // 是否包含健康分析
    "include_wealth": "boolean"     // 是否包含财运分析
  }
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "analysis_id": "string",        // 分析记录ID
    "bazi": {                       // 八字信息
      "year": "string",             // 年柱
      "month": "string",            // 月柱
      "day": "string",              // 日柱
      "hour": "string"              // 时柱
    },
    "wuxing": {                     // 五行分析
      "distribution": {             // 五行分布
        "jin": "number",
        "mu": "number",
        "shui": "number",
        "huo": "number",
        "tu": "number"
      },
      "balance": "string",          // 五行平衡状态
      "missing": ["string"],        // 缺失五行
      "excess": ["string"]          // 过旺五行
    },
    "personality": {                // 性格分析
      "traits": ["string"],         // 性格特点
      "strengths": ["string"],      // 优势
      "weaknesses": ["string"],     // 劣势
      "suggestions": ["string"]     // 建议
    },
    "fortune": {                    // 运势分析
      "overall_score": "number",    // 总体评分
      "career": {
        "score": "number",
        "description": "string",
        "suggestions": ["string"]
      },
      "marriage": {
        "score": "number",
        "description": "string",
        "suggestions": ["string"]
      },
      "health": {
        "score": "number",
        "description": "string",
        "suggestions": ["string"]
      },
      "wealth": {
        "score": "number",
        "description": "string",
        "suggestions": ["string"]
      }
    },
    "life_stages": [                // 人生阶段分析
      {
        "age_range": "string",      // 年龄段
        "description": "string",    // 描述
        "key_events": ["string"],   // 关键事件
        "suggestions": ["string"]   // 建议
      }
    ],
    "summary": "string",            // 总结
    "points_cost": "number"         // 消耗积分
  }
}
```

#### 2. 易经占卜
```
POST /api/analysis/yijing
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "question": "string",             // 占卜问题 (必填)
  "question_type": "string",        // 问题类型 (career/marriage/health/wealth/general)
  "divination_method": "string",    // 占卜方法 (coins/yarrow/number/time)
  "divination_data": {              // 占卜数据
    "coins": ["number"],            // 投币结果 (如果使用投币法)
    "numbers": ["number"],          // 数字 (如果使用数字法)
    "timestamp": "datetime"         // 占卜时间
  }
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "analysis_id": "string",
    "hexagram": {                   // 卦象信息
      "primary": {                  // 本卦
        "name": "string",           // 卦名
        "symbol": "string",         // 卦象符号
        "number": "number",         // 卦序
        "description": "string"     // 卦辞
      },
      "changed": {                  // 变卦 (如果有)
        "name": "string",
        "symbol": "string",
        "number": "number",
        "description": "string"
      },
      "changing_lines": ["number"]  // 变爻
    },
    "interpretation": {             // 解读
      "overall": "string",          // 总体解读
      "situation": "string",        // 现状分析
      "advice": "string",           // 建议
      "outcome": "string",          // 结果预测
      "timing": "string"            // 时机分析
    },
    "detailed_analysis": {          // 详细分析
      "upper_trigram": {            // 上卦
        "name": "string",
        "element": "string",
        "meaning": "string"
      },
      "lower_trigram": {            // 下卦
        "name": "string",
        "element": "string",
        "meaning": "string"
      },
      "line_analysis": [            // 爻辞分析
        {
          "position": "number",
          "type": "string",         // 阴爻/阳爻
          "meaning": "string",
          "advice": "string"
        }
      ]
    },
    "fortune_score": "number",      // 运势评分
    "points_cost": "number"
  }
}
```

#### 3. 风水分析
```
POST /api/analysis/fengshui
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "analysis_type": "string",        // 分析类型 (home/office/shop/grave)
  "location": {                     // 位置信息
    "address": "string",            // 地址
    "latitude": "number",           // 纬度
    "longitude": "number",          // 经度
    "floor": "number",              // 楼层
    "direction": "string"           // 朝向 (north/south/east/west/northeast/northwest/southeast/southwest)
  },
  "layout": {                       // 布局信息
    "rooms": [                      // 房间信息
      {
        "type": "string",           // 房间类型 (bedroom/living/kitchen/bathroom/study/office)
        "position": "string",       // 位置方位
        "size": "number",           // 面积
        "windows": "number",        // 窗户数量
        "doors": "number"           // 门的数量
      }
    ],
    "total_area": "number",         // 总面积
    "shape": "string"               // 整体形状 (square/rectangle/irregular/l_shape)
  },
  "environment": {                  // 环境信息
    "nearby_water": "boolean",      // 附近是否有水
    "nearby_mountain": "boolean",   // 附近是否有山
    "nearby_road": "boolean",       // 附近是否有道路
    "noise_level": "string",        // 噪音水平 (low/medium/high)
    "air_quality": "string"         // 空气质量 (good/fair/poor)
  }
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "analysis_id": "string",
    "overall_score": "number",      // 总体风水评分 (0-100)
    "feng_shui_analysis": {
      "qi_flow": {                  // 气流分析
        "score": "number",
        "description": "string",
        "suggestions": ["string"]
      },
      "five_elements": {            // 五行平衡
        "score": "number",
        "distribution": {
          "jin": "number",
          "mu": "number",
          "shui": "number",
          "huo": "number",
          "tu": "number"
        },
        "suggestions": ["string"]
      },
      "bagua_analysis": {           // 八卦分析
        "sectors": [
          {
            "direction": "string",  // 方位
            "element": "string",    // 对应五行
            "life_aspect": "string", // 对应生活方面
            "score": "number",      // 该方位评分
            "current_state": "string", // 当前状态
            "suggestions": ["string"]
          }
        ]
      }
    },
    "room_analysis": [              // 各房间分析
      {
        "room_type": "string",
        "score": "number",
        "strengths": ["string"],
        "weaknesses": ["string"],
        "suggestions": ["string"],
        "recommended_colors": ["string"],
        "recommended_decorations": ["string"]
      }
    ],
    "improvement_plan": {           // 改善方案
      "priority_items": [           // 优先改善项目
        {
          "item": "string",
          "priority": "string",     // high/medium/low
          "cost_estimate": "string", // 预估成本
          "expected_improvement": "number" // 预期改善分数
        }
      ],
      "layout_suggestions": ["string"], // 布局建议
      "decoration_suggestions": ["string"], // 装饰建议
      "plant_suggestions": ["string"] // 植物建议
    },
    "auspicious_dates": [           // 吉日推荐
      {
        "date": "date",
        "activity": "string",       // 适合的活动
        "description": "string"
      }
    ],
    "points_cost": "number"
  }
}
```

#### 4. 五行分析
```
POST /api/analysis/wuxing
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "use_birth_info": "boolean",      // 是否使用已保存的出生信息
  "birth_info": {                   // 临时出生信息 (可选)
    // 同八字分析的birth_info结构
  },
  "analysis_focus": ["string"]      // 分析重点 (personality/career/health/relationships/wealth)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "analysis_id": "string",
    "wuxing_distribution": {        // 五行分布
      "jin": {
        "count": "number",          // 数量
        "percentage": "number",     // 百分比
        "strength": "string"        // 强度 (weak/normal/strong/excessive)
      },
      "mu": { /* 同上 */ },
      "shui": { /* 同上 */ },
      "huo": { /* 同上 */ },
      "tu": { /* 同上 */ }
    },
    "balance_analysis": {           // 平衡分析
      "overall_balance": "number",  // 整体平衡度 (0-100)
      "dominant_element": "string", // 主导元素
      "weak_elements": ["string"],  // 弱势元素
      "missing_elements": ["string"], // 缺失元素
      "excessive_elements": ["string"] // 过旺元素
    },
    "personality_traits": {         // 性格特征
      "primary_traits": ["string"], // 主要特征
      "secondary_traits": ["string"], // 次要特征
      "potential_challenges": ["string"], // 潜在挑战
      "growth_opportunities": ["string"] // 成长机会
    },
    "life_guidance": {              // 人生指导
      "career_direction": {
        "suitable_industries": ["string"], // 适合行业
        "suitable_roles": ["string"],     // 适合职位
        "work_style": "string",           // 工作风格
        "leadership_potential": "string"  // 领导潜力
      },
      "relationship_guidance": {
        "compatibility": {
          "best_matches": ["string"],     // 最佳匹配五行
          "challenging_matches": ["string"], // 挑战性匹配
          "relationship_style": "string"  // 关系风格
        }
      },
      "health_guidance": {
        "vulnerable_areas": ["string"],   // 易患疾病部位
        "health_tips": ["string"],        // 健康建议
        "beneficial_activities": ["string"] // 有益活动
      }
    },
    "enhancement_suggestions": {    // 增强建议
      "colors": {                   // 有利颜色
        "primary": ["string"],      // 主要颜色
        "secondary": ["string"],    // 次要颜色
        "avoid": ["string"]         // 避免颜色
      },
      "directions": {               // 有利方位
        "favorable": ["string"],    // 有利方位
        "unfavorable": ["string"]   // 不利方位
      },
      "numbers": {                  // 有利数字
        "lucky": ["number"],        // 幸运数字
        "unlucky": ["number"]       // 不利数字
      },
      "lifestyle": ["string"]       // 生活方式建议
    },
    "points_cost": "number"
  }
}
```

#### 5. 紫薇斗数分析
```
POST /api/analysis/ziwei
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "use_birth_info": "boolean",
  "birth_info": {
    // 同八字分析的birth_info结构
  },
  "analysis_depth": "string"        // 分析深度 (basic/detailed/comprehensive)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "analysis_id": "string",
    "life_palace": {                // 命宫
      "palace_name": "string",      // 宫位名称
      "main_stars": ["string"],     // 主星
      "auxiliary_stars": ["string"], // 辅星
      "palace_nature": "string",    // 宫位性质
      "fortune_level": "string"     // 运势等级
    },
    "twelve_palaces": [             // 十二宫详解
      {
        "palace_name": "string",    // 宫位名称 (命宫/兄弟宫/夫妻宫等)
        "stars": [                  // 星曜
          {
            "name": "string",       // 星曜名称
            "type": "string",       // 星曜类型 (主星/辅星/煞星)
            "brightness": "string", // 亮度 (庙/旺/得地/利益/平和/不得地/陷)
            "influence": "string"   // 影响力描述
          }
        ],
        "interpretation": "string", // 宫位解读
        "fortune_score": "number",  // 运势评分
        "key_influences": ["string"] // 关键影响
      }
    ],
    "star_combinations": [          // 星曜组合
      {
        "combination_name": "string", // 组合名称
        "stars_involved": ["string"], // 涉及星曜
        "effect": "string",          // 效果
        "interpretation": "string"   // 解读
      }
    ],
    "life_trajectory": {            // 人生轨迹
      "childhood": "string",        // 童年运势
      "youth": "string",            // 青年运势
      "middle_age": "string",       // 中年运势
      "old_age": "string",          // 晚年运势
      "fortune_cycles": [           // 大运周期
        {
          "age_range": "string",    // 年龄段
          "main_theme": "string",   // 主要主题
          "opportunities": ["string"], // 机遇
          "challenges": ["string"], // 挑战
          "advice": ["string"]      // 建议
        }
      ]
    },
    "detailed_analysis": {          // 详细分析
      "career": {
        "potential": "string",      // 事业潜力
        "suitable_fields": ["string"], // 适合领域
        "career_peaks": ["string"], // 事业高峰期
        "challenges": ["string"]    // 事业挑战
      },
      "marriage": {
        "marriage_timing": "string", // 结婚时机
        "spouse_characteristics": "string", // 配偶特征
        "relationship_pattern": "string", // 感情模式
        "marriage_quality": "string" // 婚姻质量
      },
      "wealth": {
        "wealth_potential": "string", // 财富潜力
        "income_sources": ["string"], // 收入来源
        "investment_advice": "string", // 投资建议
        "financial_cycles": ["string"] // 财运周期
      },
      "health": {
        "constitution": "string",   // 体质特点
        "health_concerns": ["string"], // 健康关注点
        "wellness_advice": ["string"] // 养生建议
      }
    },
    "points_cost": "number"
  }
}
```

#### 6. 合婚分析
```
POST /api/analysis/marriage
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "person1": {                      // 第一人信息
    "name": "string",
    "gender": "string",
    "birth_year": "number",
    "birth_month": "number",
    "birth_day": "number",
    "birth_hour": "number",
    "birth_minute": "number"
  },
  "person2": {                      // 第二人信息
    // 同person1结构
  },
  "relationship_type": "string",    // 关系类型 (marriage/dating/business)
  "analysis_methods": ["string"]    // 分析方法 (bazi/ziwei/zodiac/wuxing)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "analysis_id": "string",
    "compatibility_score": "number", // 总体匹配度 (0-100)
    "compatibility_level": "string", // 匹配等级 (excellent/good/fair/poor)
    "bazi_analysis": {              // 八字合婚
      "person1_bazi": "string",     // 第一人八字
      "person2_bazi": "string",     // 第二人八字
      "compatibility": {
        "year_compatibility": "number", // 年柱匹配度
        "month_compatibility": "number", // 月柱匹配度
        "day_compatibility": "number",  // 日柱匹配度
        "hour_compatibility": "number"  // 时柱匹配度
      },
      "mutual_influence": "string", // 相互影响
      "harmony_analysis": "string"  // 和谐度分析
    },
    "wuxing_analysis": {            // 五行匹配
      "person1_wuxing": {           // 第一人五行
        "dominant": "string",
        "distribution": "object"
      },
      "person2_wuxing": {           // 第二人五行
        "dominant": "string",
        "distribution": "object"
      },
      "element_interaction": "string", // 五行相互作用
      "balance_effect": "string"    // 平衡效果
    },
    "personality_match": {          // 性格匹配
      "similarities": ["string"],   // 相似点
      "differences": ["string"],    // 差异点
      "complementarity": "string",  // 互补性
      "potential_conflicts": ["string"] // 潜在冲突
    },
    "life_aspects": {               // 生活各方面匹配
      "career": {
        "score": "number",
        "analysis": "string",
        "suggestions": ["string"]
      },
      "family": {
        "score": "number",
        "analysis": "string",
        "suggestions": ["string"]
      },
      "finance": {
        "score": "number",
        "analysis": "string",
        "suggestions": ["string"]
      },
      "communication": {
        "score": "number",
        "analysis": "string",
        "suggestions": ["string"]
      }
    },
    "relationship_guidance": {      // 关系指导
      "strengths": ["string"],      // 关系优势
      "challenges": ["string"],     // 关系挑战
      "improvement_suggestions": ["string"], // 改善建议
      "auspicious_dates": [         // 吉日推荐
        {
          "date": "date",
          "activity": "string",     // 适合活动 (engagement/marriage/moving)
          "description": "string"
        }
      ]
    },
    "long_term_forecast": {         // 长期预测
      "relationship_phases": [      // 关系阶段
        {
          "phase": "string",        // 阶段名称
          "duration": "string",     // 持续时间
          "characteristics": "string", // 特征
          "advice": "string"        // 建议
        }
      ],
      "children_prospects": "string", // 子女缘分
      "family_harmony": "string"    // 家庭和谐度
    },
    "points_cost": "number"
  }
}
```

## 💰 积分系统

### 积分记录数据结构

```javascript
// 积分记录表 (point_records)
{
  "id": "string",                   // 记录ID
  "user_id": "string",              // 用户ID
  "type": "string",                 // 类型 (earn/spend/refund/expire)
  "amount": "number",               // 积分数量 (正数为获得，负数为消费)
  "source": "string",               // 来源 (sign_in/share/invite/purchase/analysis/refund)
  "description": "string",          // 描述
  "related_id": "string",           // 关联ID (如分析记录ID、订单ID等)
  "balance_before": "number",       // 操作前余额
  "balance_after": "number",        // 操作后余额
  "expires_at": "datetime",         // 过期时间 (可选)
  "status": "string",               // 状态 (pending/completed/cancelled/expired)
  "created_at": "datetime",         // 创建时间
  "updated_at": "datetime"          // 更新时间
}
```

### 积分系统API接口

#### 1. 获取积分余额
```
GET /api/points/balance
Authorization: Bearer {token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "current_balance": "number",    // 当前余额
    "total_earned": "number",       // 累计获得
    "total_spent": "number",        // 累计消费
    "pending_points": "number",     // 待到账积分
    "expiring_soon": [              // 即将过期积分
      {
        "amount": "number",
        "expires_at": "datetime"
      }
    ]
  }
}
```

#### 2. 获取积分记录
```
GET /api/points/records
Authorization: Bearer {token}
Query Parameters:
- type: string (可选，筛选类型)
- source: string (可选，筛选来源)
- start_date: date (可选，开始日期)
- end_date: date (可选，结束日期)
- page: number (页码，默认1)
- limit: number (每页数量，默认20)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "records": [
      {
        "id": "string",
        "type": "string",
        "amount": "number",
        "source": "string",
        "description": "string",
        "balance_after": "number",
        "created_at": "datetime"
      }
    ],
    "pagination": {
      "current_page": "number",
      "total_pages": "number",
      "total_count": "number"
    },
    "summary": {
      "total_earned": "number",
      "total_spent": "number",
      "net_change": "number"
    }
  }
}
```

#### 3. 签到获取积分
```
POST /api/points/sign-in
Authorization: Bearer {token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "points_earned": "number",      // 获得积分
    "consecutive_days": "number",   // 连续签到天数
    "total_sign_days": "number",    // 总签到天数
    "next_reward": {                // 下次奖励
      "days_needed": "number",      // 还需天数
      "reward_points": "number"     // 奖励积分
    },
    "current_balance": "number"     // 当前余额
  }
}
```

#### 4. 分享获取积分
```
POST /api/points/share
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "share_type": "string",           // 分享类型 (analysis/app/article)
  "shared_content_id": "string",    // 分享内容ID
  "platform": "string"             // 分享平台 (wechat/moments/qq/weibo)
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "points_earned": "number",
    "daily_share_count": "number",  // 今日分享次数
    "daily_limit": "number",        // 每日限制
    "current_balance": "number"
  }
}
```

## 🏢 企业微信集成

### 企业用户数据结构

```javascript
// 企业用户表 (enterprise_users)
{
  "id": "string",                   // 用户ID
  "corp_id": "string",              // 企业ID
  "user_id": "string",              // 企业微信用户ID
  "name": "string",                 // 姓名
  "department": ["number"],         // 部门ID列表
  "position": "string",             // 职位
  "mobile": "string",               // 手机号
  "email": "string",                // 邮箱
  "avatar": "string",               // 头像URL
  "status": "number",               // 状态 (1:已激活, 2:已禁用, 4:未激活, 5:退出企业)
  "is_leader_in_dept": ["number"],  // 在部门中是否为领导
  "main_department": "number",      // 主部门
  "permissions": ["string"],        // 权限列表
  "created_at": "datetime",
  "updated_at": "datetime"
}

// 部门表 (departments)
{
  "id": "number",                   // 部门ID
  "corp_id": "string",              // 企业ID
  "name": "string",                 // 部门名称
  "parent_id": "number",            // 父部门ID
  "order": "number",                // 排序
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### 企业微信API接口

#### 1. 企业微信登录
```
POST /api/wxwork/auth
Content-Type: application/json

Request:
{
  "code": "string",                 // 企业微信授权码
  "corp_id": "string"               // 企业ID
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "user": {
      "id": "string",
      "corp_id": "string",
      "user_id": "string",
      "name": "string",
      "department": ["number"],
      "position": "string",
      "avatar": "string",
      "permissions": ["string"]
    },
    "token": "string",
    "refresh_token": "string",
    "expires_in": "number"
  }
}
```

#### 2. 获取部门列表
```
GET /api/wxwork/departments
Authorization: Bearer {token}
Query Parameters:
- parent_id: number (可选，父部门ID，默认获取所有)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "departments": [
      {
        "id": "number",
        "name": "string",
        "parent_id": "number",
        "order": "number",
        "member_count": "number",    // 成员数量
        "sub_departments": [         // 子部门 (如果有)
          {
            "id": "number",
            "name": "string",
            "member_count": "number"
          }
        ]
      }
    ]
  }
}
```

#### 3. 获取部门成员
```
GET /api/wxwork/departments/{department_id}/members
Authorization: Bearer {token}
Query Parameters:
- fetch_child: boolean (是否获取子部门成员，默认false)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "members": [
      {
        "user_id": "string",
        "name": "string",
        "department": ["number"],
        "position": "string",
        "mobile": "string",
        "email": "string",
        "avatar": "string",
        "status": "number",
        "is_leader": "boolean"
      }
    ],
    "department_info": {
      "id": "number",
      "name": "string",
      "total_members": "number"
    }
  }
}
```

#### 4. 发送企业消息
```
POST /api/wxwork/messages
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "message_type": "string",         // 消息类型 (text/image/file/news)
  "recipients": {                   // 接收者
    "to_user": ["string"],          // 用户ID列表
    "to_party": ["number"],         // 部门ID列表
    "to_tag": ["string"]            // 标签ID列表
  },
  "content": {                      // 消息内容
    "text": "string",               // 文本内容 (text类型)
    "media_id": "string",           // 媒体文件ID (image/file类型)
    "articles": [                   // 图文消息 (news类型)
      {
        "title": "string",
        "description": "string",
        "url": "string",
        "pic_url": "string"
      }
    ]
  },
  "safe": "number",                 // 是否保密消息 (0:否, 1:是)
  "enable_id_trans": "number",      // 是否开启ID转译 (0:否, 1:是)
  "enable_duplicate_check": "number" // 是否开启重复消息检查
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "message_id": "string",         // 消息ID
    "invalid_user": ["string"],     // 无效用户
    "invalid_party": ["number"],    // 无效部门
    "invalid_tag": ["string"]       // 无效标签
  }
}
```

## 📊 后台管理系统

### 管理员数据结构

```javascript
// 管理员表 (admins)
{
  "id": "string",                   // 管理员ID
  "username": "string",             // 用户名
  "email": "string",                // 邮箱
  "password_hash": "string",        // 密码哈希
  "name": "string",                 // 姓名
  "avatar": "string",               // 头像
  "role": "string",                 // 角色 (super_admin/admin/operator)
  "permissions": ["string"],        // 权限列表
  "status": "string",               // 状态 (active/inactive/locked)
  "last_login_at": "datetime",      // 最后登录时间
  "last_login_ip": "string",        // 最后登录IP
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### 后台管理API接口

#### 1. 管理员登录
```
POST /api/admin/auth/login
Content-Type: application/json

Request:
{
  "username": "string",             // 用户名或邮箱
  "password": "string",             // 密码
  "captcha": "string",              // 验证码 (可选)
  "remember_me": "boolean"          // 是否记住登录
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "admin": {
      "id": "string",
      "username": "string",
      "name": "string",
      "email": "string",
      "avatar": "string",
      "role": "string",
      "permissions": ["string"]
    },
    "token": "string",
    "expires_in": "number"
  }
}
```

#### 2. 获取系统统计
```
GET /api/admin/dashboard/stats
Authorization: Bearer {token}
Query Parameters:
- period: string (统计周期: today/week/month/year)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "user_stats": {
      "total_users": "number",      // 总用户数
      "new_users": "number",        // 新增用户
      "active_users": "number",     // 活跃用户
      "vip_users": "number"         // VIP用户
    },
    "analysis_stats": {
      "total_analyses": "number",   // 总分析次数
      "today_analyses": "number",   // 今日分析
      "popular_types": [            // 热门分析类型
        {
          "type": "string",
          "count": "number",
          "percentage": "number"
        }
      ]
    },
    "revenue_stats": {
      "total_revenue": "number",    // 总收入
      "today_revenue": "number",    // 今日收入
      "points_sold": "number",      // 积分销售
      "vip_revenue": "number"       // VIP收入
    },
    "system_stats": {
      "api_calls": "number",        // API调用次数
      "error_rate": "number",       // 错误率
      "avg_response_time": "number", // 平均响应时间
      "storage_usage": "number"     // 存储使用量
    }
  }
}
```

#### 3. 用户管理
```
GET /api/admin/users
Authorization: Bearer {token}
Query Parameters:
- page: number (页码)
- limit: number (每页数量)
- search: string (搜索关键词)
- status: string (用户状态筛选)
- vip_level: number (VIP等级筛选)
- register_start: date (注册开始日期)
- register_end: date (注册结束日期)

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "users": [
      {
        "id": "string",
        "nickname": "string",
        "avatar_url": "string",
        "phone": "string",
        "email": "string",
        "vip_level": "number",
        "points": "number",
        "status": "string",
        "last_login_time": "datetime",
        "register_time": "datetime",
        "analysis_count": "number",   // 分析次数
        "total_spent": "number"       // 总消费
      }
    ],
    "pagination": {
      "current_page": "number",
      "total_pages": "number",
      "total_count": "number"
    },
    "summary": {
      "total_users": "number",
      "active_users": "number",
      "vip_users": "number",
      "total_points": "number"
    }
  }
}
```

#### 4. 分析记录管理
```
GET /api/admin/analyses
Authorization: Bearer {token}
Query Parameters:
- page: number
- limit: number
- type: string (分析类型筛选)
- user_id: string (用户ID筛选)
- start_date: date
- end_date: date

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "analyses": [
      {
        "id": "string",
        "user_id": "string",
        "user_nickname": "string",
        "analysis_type": "string",
        "score": "number",
        "points_cost": "number",
        "processing_time": "number",
        "created_at": "datetime"
      }
    ],
    "pagination": {
      "current_page": "number",
      "total_pages": "number",
      "total_count": "number"
    },
    "statistics": {
      "total_analyses": "number",
      "avg_score": "number",
      "total_points_cost": "number",
      "avg_processing_time": "number"
    }
  }
}
```

#### 5. 系统配置管理
```
GET /api/admin/config
Authorization: Bearer {token}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "point_config": {
      "sign_in_reward": "number",   // 签到奖励
      "share_reward": "number",     // 分享奖励
      "invite_reward": "number",    // 邀请奖励
      "analysis_costs": {           // 分析消费
        "bazi": "number",
        "yijing": "number",
        "fengshui": "number",
        "wuxing": "number",
        "ziwei": "number",
        "marriage": "number"
      }
    },
    "vip_config": {
      "levels": [
        {
          "level": "number",
          "name": "string",
          "price": "number",
          "duration": "number",       // 天数
          "benefits": ["string"]      // 权益
        }
      ]
    },
    "ai_config": {
      "model_version": "string",
      "max_tokens": "number",
      "temperature": "number",
      "max_history": "number"
    },
    "system_config": {
      "maintenance_mode": "boolean",
      "registration_enabled": "boolean",
      "max_daily_analyses": "number"
    }
  }
}
```

#### 6. 更新系统配置
```
PUT /api/admin/config
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "config_type": "string",          // 配置类型 (point/vip/ai/system)
  "config_data": "object"           // 配置数据
}

Response:
{
  "status": "success|error",
  "message": "string",
  "data": {
    "updated_config": "object"
  }
}
```

## 🚨 错误码定义

### 通用错误码
```javascript
{
  // 认证相关 (1000-1099)
  "1001": "未授权访问",
  "1002": "Token已过期",
  "1003": "Token无效",
  "1004": "权限不足",
  "1005": "账户已被禁用",

  // 参数相关 (1100-1199)
  "1101": "参数缺失",
  "1102": "参数格式错误",
  "1103": "参数值超出范围",
  "1104": "必填参数为空",
  "1105": "参数类型错误",

  // 用户相关 (1200-1299)
  "1201": "用户不存在",
  "1202": "用户已存在",
  "1203": "密码错误",
  "1204": "用户信息不完整",
  "1205": "出生信息未设置",
  "1206": "积分余额不足",
  "1207": "VIP已过期",

  // 业务相关 (1300-1399)
  "1301": "分析次数已达上限",
  "1302": "分析类型不支持",
  "1303": "数据处理失败",
  "1304": "AI服务暂时不可用",
  "1305": "文件上传失败",
  "1306": "数据格式不正确",

  // 系统相关 (1400-1499)
  "1401": "服务器内部错误",
  "1402": "数据库连接失败",
  "1403": "外部服务调用失败",
  "1404": "资源不存在",
  "1405": "请求超时",
  "1406": "系统维护中",

  // 企业微信相关 (1500-1599)
  "1501": "企业微信认证失败",
  "1502": "企业不存在",
  "1503": "部门不存在",
  "1504": "用户不在企业中",
  "1505": "消息发送失败"
}
```

## 🗄️ 数据库设计

### 主要数据表

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  openid VARCHAR(100) UNIQUE NOT NULL,
  unionid VARCHAR(100),
  nickname VARCHAR(100),
  avatar_url TEXT,
  gender TINYINT DEFAULT 0,
  country VARCHAR(50),
  province VARCHAR(50),
  city VARCHAR(50),
  language VARCHAR(20),
  phone VARCHAR(20),
  email VARCHAR(100),
  status TINYINT DEFAULT 0,
  vip_level TINYINT DEFAULT 0,
  vip_expire_time DATETIME,
  points INT DEFAULT 0,
  total_points INT DEFAULT 0,
  register_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_login_time DATETIME,
  login_count INT DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_openid (openid),
  INDEX idx_status (status),
  INDEX idx_vip_level (vip_level),
  INDEX idx_register_time (register_time)
);
```

#### 2. 出生信息表 (birth_info)
```sql
CREATE TABLE birth_info (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  name VARCHAR(50) NOT NULL,
  gender ENUM('男', '女') NOT NULL,
  birth_year SMALLINT NOT NULL,
  birth_month TINYINT NOT NULL,
  birth_day TINYINT NOT NULL,
  birth_hour TINYINT NOT NULL,
  birth_minute TINYINT DEFAULT 0,
  birth_timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
  lunar_year SMALLINT,
  lunar_month TINYINT,
  lunar_day TINYINT,
  lunar_leap_month BOOLEAN DEFAULT FALSE,
  zodiac VARCHAR(10),
  constellation VARCHAR(20),
  lucky_number TINYINT,
  bazi JSON,
  wuxing JSON,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_birth_date (birth_year, birth_month, birth_day)
);
```

#### 3. 聊天会话表 (chat_sessions)
```sql
CREATE TABLE chat_sessions (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  title VARCHAR(200),
  status ENUM('active', 'ended', 'archived') DEFAULT 'active',
  message_count INT DEFAULT 0,
  total_tokens INT DEFAULT 0,
  total_cost DECIMAL(10,4) DEFAULT 0,
  start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  end_time DATETIME,
  last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
  metadata JSON,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_last_activity (last_activity),
  INDEX idx_created_at (created_at)
);
```

#### 4. 聊天记录表 (chat_messages)
```sql
CREATE TABLE chat_messages (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  session_id VARCHAR(36) NOT NULL,
  message_type ENUM('user', 'ai', 'system') NOT NULL,
  content TEXT NOT NULL,
  intent VARCHAR(50),
  context JSON,
  attachments JSON,
  metadata JSON,
  feedback JSON,
  is_deleted BOOLEAN DEFAULT FALSE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
  INDEX idx_user_session (user_id, session_id),
  INDEX idx_session_id (session_id),
  INDEX idx_created_at (created_at),
  INDEX idx_message_type (message_type),
  INDEX idx_intent (intent)
);
```

#### 5. 意图配置表 (intent_configs)
```sql
CREATE TABLE intent_configs (
  id VARCHAR(36) PRIMARY KEY,
  intent_name VARCHAR(100) NOT NULL UNIQUE,
  keywords JSON,
  patterns JSON,
  confidence_threshold DECIMAL(3,2) DEFAULT 0.8,
  response_templates JSON,
  actions JSON,
  requires_birth_info BOOLEAN DEFAULT FALSE,
  points_cost INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  priority INT DEFAULT 0,
  usage_count INT DEFAULT 0,
  success_count INT DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_intent_name (intent_name),
  INDEX idx_is_active (is_active),
  INDEX idx_priority (priority)
);
```

#### 6. AI配置表 (ai_configs)
```sql
CREATE TABLE ai_configs (
  id VARCHAR(36) PRIMARY KEY,
  config_type ENUM('model', 'chat', 'intent', 'quick_actions') NOT NULL,
  config_name VARCHAR(100) NOT NULL,
  config_data JSON NOT NULL,
  version VARCHAR(20) DEFAULT '1.0',
  is_active BOOLEAN DEFAULT TRUE,
  effective_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(36),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_config_type (config_type),
  INDEX idx_is_active (is_active),
  INDEX idx_effective_time (effective_time),
  UNIQUE KEY uk_type_name (config_type, config_name)
);
```

#### 7. 分析记录表 (analysis_records)
```sql
CREATE TABLE analysis_records (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  analysis_type ENUM('bazi', 'yijing', 'fengshui', 'wuxing', 'ziwei', 'marriage') NOT NULL,
  input_data JSON NOT NULL,
  result_data JSON NOT NULL,
  score DECIMAL(5,2),
  summary TEXT,
  suggestions JSON,
  points_cost INT DEFAULT 0,
  processing_time INT,
  is_shared BOOLEAN DEFAULT FALSE,
  share_count INT DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_type (user_id, analysis_type),
  INDEX idx_created_at (created_at),
  INDEX idx_analysis_type (analysis_type)
);
```

#### 8. 积分记录表 (point_records)
```sql
CREATE TABLE point_records (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  type ENUM('earn', 'spend', 'refund', 'expire') NOT NULL,
  amount INT NOT NULL,
  source ENUM('sign_in', 'share', 'invite', 'purchase', 'analysis', 'refund') NOT NULL,
  description VARCHAR(200),
  related_id VARCHAR(36),
  balance_before INT NOT NULL,
  balance_after INT NOT NULL,
  expires_at DATETIME,
  status ENUM('pending', 'completed', 'cancelled', 'expired') DEFAULT 'completed',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_type (user_id, type),
  INDEX idx_created_at (created_at),
  INDEX idx_source (source),
  INDEX idx_status (status)
);
```

### 企业微信相关表

#### 9. 企业用户表 (enterprise_users)
```sql
CREATE TABLE enterprise_users (
  id VARCHAR(36) PRIMARY KEY,
  corp_id VARCHAR(100) NOT NULL,
  user_id VARCHAR(100) NOT NULL,
  name VARCHAR(100) NOT NULL,
  department JSON,
  position VARCHAR(100),
  mobile VARCHAR(20),
  email VARCHAR(100),
  avatar TEXT,
  status TINYINT DEFAULT 1,
  is_leader_in_dept JSON,
  main_department INT,
  permissions JSON,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY uk_corp_user (corp_id, user_id),
  INDEX idx_corp_id (corp_id),
  INDEX idx_status (status)
);
```

#### 10. 部门表 (departments)
```sql
CREATE TABLE departments (
  id INT PRIMARY KEY,
  corp_id VARCHAR(100) NOT NULL,
  name VARCHAR(100) NOT NULL,
  parent_id INT DEFAULT 0,
  order_num INT DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_corp_parent (corp_id, parent_id),
  INDEX idx_corp_id (corp_id)
);
```

### 知识库相关表

#### 11. 知识库文档表 (knowledge_documents)
```sql
CREATE TABLE knowledge_documents (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(500) NOT NULL,
  content LONGTEXT NOT NULL,
  summary TEXT,
  category VARCHAR(100) NOT NULL,
  tags JSON,
  keywords JSON,
  difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
  content_type ENUM('text', 'markdown', 'html') DEFAULT 'markdown',
  source VARCHAR(200),
  author VARCHAR(100),
  version VARCHAR(20) DEFAULT '1.0',
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  view_count INT DEFAULT 0,
  like_count INT DEFAULT 0,
  embedding_vector JSON,
  last_updated_by VARCHAR(36),
  published_at DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_category (category),
  INDEX idx_status (status),
  INDEX idx_difficulty (difficulty_level),
  INDEX idx_published_at (published_at),
  INDEX idx_view_count (view_count),
  FULLTEXT idx_content_search (title, content, summary)
);
```

#### 12. 知识库问答对表 (knowledge_qa_pairs)
```sql
CREATE TABLE knowledge_qa_pairs (
  id VARCHAR(36) PRIMARY KEY,
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  category VARCHAR(100) NOT NULL,
  tags JSON,
  difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
  confidence_score DECIMAL(3,2) DEFAULT 0.8,
  usage_count INT DEFAULT 0,
  success_count INT DEFAULT 0,
  success_rate DECIMAL(5,2) DEFAULT 0,
  related_documents JSON,
  embedding_vector JSON,
  is_active BOOLEAN DEFAULT TRUE,
  created_by VARCHAR(36),
  reviewed_by VARCHAR(36),
  reviewed_at DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_category (category),
  INDEX idx_is_active (is_active),
  INDEX idx_difficulty (difficulty),
  INDEX idx_usage_count (usage_count),
  INDEX idx_success_rate (success_rate),
  FULLTEXT idx_qa_search (question, answer)
);
```

#### 13. 知识库搜索日志表 (knowledge_search_logs)
```sql
CREATE TABLE knowledge_search_logs (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36),
  session_id VARCHAR(36),
  query TEXT NOT NULL,
  search_type ENUM('semantic', 'keyword', 'hybrid') DEFAULT 'hybrid',
  results_count INT DEFAULT 0,
  top_result_id VARCHAR(36),
  top_result_type ENUM('document', 'qa_pair'),
  click_through BOOLEAN DEFAULT FALSE,
  user_feedback ENUM('helpful', 'not_helpful', 'irrelevant'),
  response_time INT,
  search_filters JSON,
  results_data JSON,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_session_id (session_id),
  INDEX idx_search_type (search_type),
  INDEX idx_created_at (created_at),
  INDEX idx_user_feedback (user_feedback),
  FULLTEXT idx_query_search (query)
);
```

#### 14. 知识库用户收藏表 (knowledge_bookmarks)
```sql
CREATE TABLE knowledge_bookmarks (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  content_id VARCHAR(36) NOT NULL,
  content_type ENUM('document', 'qa_pair') NOT NULL,
  notes TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY uk_user_content (user_id, content_id, content_type),
  INDEX idx_user_id (user_id),
  INDEX idx_content (content_id, content_type),
  INDEX idx_created_at (created_at)
);
```

#### 15. 知识库反馈表 (knowledge_feedback)
```sql
CREATE TABLE knowledge_feedback (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36),
  content_id VARCHAR(36) NOT NULL,
  content_type ENUM('document', 'qa_pair') NOT NULL,
  feedback_type ENUM('helpful', 'not_helpful', 'incorrect', 'outdated') NOT NULL,
  rating TINYINT CHECK (rating >= 1 AND rating <= 5),
  comment TEXT,
  search_query TEXT,
  context VARCHAR(200),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_content (content_id, content_type),
  INDEX idx_feedback_type (feedback_type),
  INDEX idx_rating (rating),
  INDEX idx_created_at (created_at)
);
```

#### 16. 知识库分类表 (knowledge_categories)
```sql
CREATE TABLE knowledge_categories (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  display_name VARCHAR(200) NOT NULL,
  description TEXT,
  icon VARCHAR(100),
  parent_id VARCHAR(36),
  sort_order INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (parent_id) REFERENCES knowledge_categories(id) ON DELETE SET NULL,
  INDEX idx_parent_id (parent_id),
  INDEX idx_is_active (is_active),
  INDEX idx_sort_order (sort_order)
);
```

## 🔧 技术规范

### API设计规范

#### 1. RESTful API设计
- 使用标准HTTP方法 (GET, POST, PUT, DELETE)
- URL使用名词复数形式
- 版本控制: `/api/v1/`
- 统一的响应格式

#### 2. 认证授权
- JWT Token认证
- Bearer Token格式
- Token自动刷新机制
- 权限控制中间件

#### 3. 数据验证
- 请求参数验证
- 数据类型检查
- 业务规则验证
- 安全过滤

#### 4. 错误处理
- 统一错误码
- 详细错误信息
- 错误日志记录
- 友好错误提示

### 性能优化

#### 1. 数据库优化
- 合理的索引设计
- 查询优化
- 连接池配置
- 读写分离

#### 2. 缓存策略
- Redis缓存
- 用户信息缓存
- 分析结果缓存
- 配置信息缓存

#### 3. API优化
- 响应压缩
- 分页查询
- 异步处理
- 限流控制

## 🚀 部署说明

### 环境要求
- Node.js 16+
- MySQL 8.0+
- Redis 6.0+
- Nginx 1.18+

### 配置文件示例
```javascript
// config/production.js
module.exports = {
  database: {
    host: 'localhost',
    port: 3306,
    username: 'root',
    password: 'password',
    database: 'guali_qiankun',
    dialect: 'mysql',
    pool: {
      max: 20,
      min: 5,
      acquire: 30000,
      idle: 10000
    }
  },
  redis: {
    host: 'localhost',
    port: 6379,
    password: '',
    db: 0
  },
  jwt: {
    secret: 'your-jwt-secret',
    expiresIn: '7d'
  },
  ai: {
    apiKey: 'your-ai-api-key',
    baseUrl: 'https://api.openai.com/v1',
    model: 'gpt-3.5-turbo'
  },
  wxwork: {
    corpId: 'your-corp-id',
    corpSecret: 'your-corp-secret'
  }
}
```

### Docker部署
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### Nginx配置
```nginx
server {
    listen 80;
    server_name api.guali-qiankun.com;

    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 📝 开发指南

### 1. 项目初始化
```bash
# 克隆项目
git clone <repository-url>
cd guali-qiankun-api

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env

# 数据库迁移
npm run migrate

# 启动开发服务器
npm run dev
```

### 2. 代码规范
- ESLint + Prettier
- Git Hooks (pre-commit)
- 单元测试覆盖率 > 80%
- API文档自动生成

### 3. 测试
```bash
# 单元测试
npm run test

# 集成测试
npm run test:integration

# 覆盖率报告
npm run test:coverage
```

---

## 📋 总结

本文档详细描述了"卦里乾坤"小程序的完整API接口规范，包括：

- **用户管理系统** - 完整的用户认证、信息管理
- **出生信息管理** - 专业的命理数据收集和验证
- **AI问答系统** - 智能对话和意图识别
- **命理分析系统** - 八字、易经、风水等专业分析
- **积分系统** - 完整的积分获取和消费机制
- **企业微信集成** - 企业版功能支持
- **后台管理系统** - 全面的运营管理功能

所有接口都遵循RESTful设计规范，提供了详细的请求/响应格式、错误处理、数据库设计和部署说明，可以直接用于API开发和后台管理界面生成。
