Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#9575cd",
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页",
        iconPath: "https://img.icons8.com/fluency-systems-regular/96/999999/home.png",
        selectedIconPath: "https://img.icons8.com/fluency-systems-filled/96/9575cd/home.png"
      },
      {
        pagePath: "/pages/ai-chat/ai-chat",
        text: "AI问答",
        iconPath: "https://img.icons8.com/fluency-systems-regular/96/999999/chatbot.png",
        selectedIconPath: "https://img.icons8.com/fluency-systems-filled/96/9575cd/chatbot.png",
        isSpecial: true
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        iconPath: "https://img.icons8.com/fluency-systems-regular/96/999999/user-male-circle.png",
        selectedIconPath: "https://img.icons8.com/fluency-systems-filled/96/9575cd/user-male-circle.png"
      }
    ]
  },
  attached() {
    this.setSelected()
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      wx.switchTab({url})
      this.setData({
        selected: data.index
      })
    },
    setSelected() {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const url = '/' + currentPage.route
      const selected = this.data.list.findIndex(item => {
        if (url.includes('ai-chat') && item.pagePath.includes('ai-chat')) {
          return true
        }
        return item.pagePath === url
      })
      if (selected !== -1) {
        this.setData({ selected })
      }
    }
  }
}) 