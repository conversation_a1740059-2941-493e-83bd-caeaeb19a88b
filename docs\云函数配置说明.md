# 企业微信聊天集成云函数配置说明

## 概述

为了支持企业微信小程序的智能聊天集成功能，需要配置以下云函数。这些云函数提供了企业微信API调用、AI对话生成、用户信息管理等核心功能。

## 必需的云函数列表

### 1. 用户信息相关

#### `getWxWorkUserInfo`
**功能：** 获取企业微信用户信息
**入参：**
```javascript
{
  code: string // 企业微信登录code
}
```
**出参：**
```javascript
{
  success: boolean,
  data: {
    userInfo: {
      openid: string,
      userid: string,
      name: string,
      avatar: string
    },
    wxworkUserInfo: {
      userid: string,
      name: string,
      department: array,
      position: string,
      mobile: string,
      email: string,
      status: string,
      isAdmin: boolean
    }
  }
}
```

#### `getUserDetail`
**功能：** 获取用户详细信息
**入参：**
```javascript
{
  userId: string // 企业微信用户ID
}
```
**出参：**
```javascript
{
  success: boolean,
  data: {
    userid: string,
    name: string,
    department: array,
    position: string,
    mobile: string,
    email: string,
    avatar: string,
    status: string
  }
}
```

#### `getUserProfileInMiniapp`
**功能：** 获取用户在小程序中的个人资料
**入参：**
```javascript
{
  userId: string // 企业微信用户ID
}
```
**出参：**
```javascript
{
  success: boolean,
  data: {
    userId: string,
    birthInfo: {
      name: string,
      birthDate: string,
      birthTime: string,
      zodiac: string
    },
    interests: array,
    recentUsage: array,
    preferences: object
  }
}
```

### 2. 通讯录相关

#### `getContactList`
**功能：** 获取企业通讯录列表
**入参：**
```javascript
{
  departmentId: string // 部门ID，可选
}
```
**出参：**
```javascript
{
  success: boolean,
  data: [
    {
      userid: string,
      name: string,
      department: array,
      position: string,
      mobile: string,
      email: string,
      avatar: string,
      status: string
    }
  ]
}
```

#### `getDepartmentList`
**功能：** 获取部门列表
**入参：**
```javascript
{}
```
**出参：**
```javascript
{
  success: boolean,
  data: [
    {
      id: string,
      name: string,
      parentid: string,
      order: number
    }
  ]
}
```

### 3. AI对话相关

#### `generateAIReply`
**功能：** 生成AI回复
**入参：**
```javascript
{
  message: string, // 用户消息
  context: {
    userInfo: object, // 企业微信用户信息
    userProfile: object, // 小程序用户资料
    chatHistory: array, // 聊天历史
    chatType: string // 聊天类型
  },
  type: string // 场景类型
}
```
**出参：**
```javascript
{
  success: boolean,
  data: {
    reply: string, // AI回复内容
    suggestions: array, // 建议问题
    metadata: object // 元数据
  }
}
```

### 4. 消息发送相关

#### `sendWxWorkMessage`
**功能：** 发送企业微信消息
**入参：**
```javascript
{
  touser: string, // 接收用户ID
  msgtype: string, // 消息类型
  text: {
    content: string // 消息内容
  }
}
```
**出参：**
```javascript
{
  success: boolean,
  data: {
    errcode: number,
    errmsg: string
  }
}
```

### 5. 审批流程相关

#### `getApprovalList`
**功能：** 获取审批列表
**入参：**
```javascript
{
  type: string, // 审批类型
  status: string, // 审批状态
  date: string, // 日期筛选
  page: number, // 页码
  pageSize: number // 每页大小
}
```

#### `createApproval`
**功能：** 创建审批
**入参：**
```javascript
{
  type: string,
  title: string,
  content: string,
  approvers: array,
  data: object
}
```

#### `handleApproval`
**功能：** 处理审批
**入参：**
```javascript
{
  approvalId: string,
  action: string, // approve/reject
  comment: string
}
```

## 云函数实现示例

### `generateAIReply` 实现示例

```javascript
// 云函数入口文件
const cloud = require('wx-server-sdk')
const { Configuration, OpenAIApi } = require('openai')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const { message, context: chatContext, type } = event
    
    // 构建AI提示词
    let prompt = buildPrompt(message, chatContext, type)
    
    // 调用AI API生成回复
    const aiReply = await generateAIResponse(prompt)
    
    // 保存对话记录
    await saveConversation({
      userMessage: message,
      aiReply: aiReply,
      context: chatContext,
      timestamp: new Date()
    })
    
    return {
      success: true,
      data: {
        reply: aiReply,
        suggestions: generateSuggestions(chatContext),
        metadata: {
          type: type,
          timestamp: new Date()
        }
      }
    }
  } catch (error) {
    console.error('AI回复生成失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

function buildPrompt(message, context, type) {
  let prompt = '你是卦里乾坤企业版的AI助手，专门为企业用户提供命理分析服务。\n\n'
  
  // 根据用户信息定制提示词
  if (context.userInfo) {
    prompt += `用户信息：\n`
    prompt += `- 姓名：${context.userInfo.name}\n`
    if (context.userInfo.department) {
      prompt += `- 部门：${context.userInfo.department.join(', ')}\n`
    }
    if (context.userInfo.position) {
      prompt += `- 职位：${context.userInfo.position}\n`
    }
  }
  
  // 根据小程序资料定制
  if (context.userProfile && context.userProfile.birthInfo) {
    prompt += `\n出生信息：\n`
    prompt += `- 出生日期：${context.userProfile.birthInfo.birthDate}\n`
    prompt += `- 出生时间：${context.userProfile.birthInfo.birthTime}\n`
    prompt += `- 生肖：${context.userProfile.birthInfo.zodiac}\n`
  }
  
  // 添加聊天历史
  if (context.chatHistory && context.chatHistory.length > 0) {
    prompt += `\n最近对话：\n`
    context.chatHistory.slice(-3).forEach(msg => {
      prompt += `${msg.type === 'user' ? '用户' : 'AI'}：${msg.content}\n`
    })
  }
  
  prompt += `\n当前问题：${message}\n\n`
  prompt += '请提供专业、个性化的回复，结合用户的具体情况给出建议。'
  
  return prompt
}

async function generateAIResponse(prompt) {
  // 这里可以集成各种AI服务
  // 例如：OpenAI GPT、百度文心一言、阿里通义千问等
  
  // 示例：使用OpenAI
  const configuration = new Configuration({
    apiKey: process.env.OPENAI_API_KEY,
  })
  const openai = new OpenAIApi(configuration)
  
  const completion = await openai.createChatCompletion({
    model: "gpt-3.5-turbo",
    messages: [
      {
        role: "system",
        content: "你是一个专业的命理分析师，擅长八字、紫微斗数、风水等传统文化。"
      },
      {
        role: "user",
        content: prompt
      }
    ],
    max_tokens: 500,
    temperature: 0.7,
  })
  
  return completion.data.choices[0].message.content
}

function generateSuggestions(context) {
  const suggestions = [
    "分析我的今日运势",
    "给我一些风水建议",
    "解读我的八字命盘"
  ]
  
  // 根据用户资料生成个性化建议
  if (context.userProfile && context.userProfile.interests) {
    context.userProfile.interests.forEach(interest => {
      if (interest === '婚姻') {
        suggestions.push("分析我的婚姻运势")
      } else if (interest === '事业') {
        suggestions.push("预测我的事业发展")
      }
    })
  }
  
  return suggestions.slice(0, 3)
}

async function saveConversation(data) {
  try {
    await db.collection('conversations').add({
      data: data
    })
  } catch (error) {
    console.error('保存对话记录失败:', error)
  }
}
```

## 环境配置

### 1. 企业微信API配置

在云函数环境变量中配置：
```
CORP_ID=企业ID
CORP_SECRET=应用密钥
AGENT_ID=应用ID
```

### 2. AI服务配置

根据使用的AI服务配置相应密钥：
```
OPENAI_API_KEY=OpenAI密钥
BAIDU_API_KEY=百度API密钥
BAIDU_SECRET_KEY=百度Secret密钥
```

### 3. 数据库权限配置

确保云函数有以下数据库集合的读写权限：
- `users` - 用户信息
- `conversations` - 对话记录
- `approvals` - 审批记录
- `departments` - 部门信息
- `contacts` - 联系人信息

## 部署步骤

1. **创建云函数**
   ```bash
   # 在小程序开发者工具中创建云函数
   # 或使用命令行工具
   tcb fn deploy --name generateAIReply
   ```

2. **安装依赖**
   ```bash
   # 在云函数目录下
   npm install wx-server-sdk
   npm install openai  # 如果使用OpenAI
   ```

3. **配置环境变量**
   在云开发控制台中设置环境变量

4. **测试云函数**
   使用云开发控制台的测试功能验证各个云函数

## 注意事项

1. **权限控制**：确保云函数有适当的权限访问企业微信API
2. **错误处理**：所有云函数都应包含完善的错误处理机制
3. **性能优化**：对于频繁调用的云函数，考虑添加缓存机制
4. **安全性**：敏感信息应使用环境变量存储，不要硬编码在代码中
5. **监控日志**：建议开启云函数日志监控，便于排查问题

## 升级维护

- 定期更新依赖包版本
- 监控云函数执行情况和性能
- 根据业务需求调整AI模型和提示词
- 定期备份重要数据

这些云函数是企业微信聊天集成功能的核心支撑，请确保正确配置和部署。 