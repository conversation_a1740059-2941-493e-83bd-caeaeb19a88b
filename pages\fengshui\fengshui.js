// pages/fengshui/fengshui.js
const app = getApp()
const fengshuiCalculator = require('../../utils/fengshui')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    floorPlan: null,
    layoutResult: null,
    loading: false,
    error: null,
    birthInfo: null,
    fengshuiResult: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadBirthInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadBirthInfo();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '卦里乾坤 - 专业的风水分析工具',
      path: '/pages/fengshui/fengshui'
    }
  },

  onDateChange(e) {
    this.setData({
      birthDate: e.detail.value
    })
  },

  onTimeChange(e) {
    this.setData({
      birthTime: e.detail.value
    })
  },

  onGenderChange(e) {
    this.setData({
      gender: e.detail.value
    })
  },

  // 加载出生信息
  loadBirthInfo() {
    const birthInfo = wx.getStorageSync('birthInfo');
    if (birthInfo) {
      this.setData({ birthInfo });
      this.calculateFengshui(birthInfo);
    }
  },

  // 计算风水
  calculateFengshui(birthInfo) {
    if (!birthInfo) return;

    this.setData({ loading: true, error: null });

    try {
      const result = fengshuiCalculator.calculate(
        birthInfo.birthDate,
        birthInfo.birthTime,
        birthInfo.gender
      );

      this.setData({
        fengshuiResult: result,
        loading: false
      });
    } catch (error) {
      console.error('风水分析错误:', error);
      this.setData({
        error: '计算风水时出现错误，请检查输入信息是否正确',
        loading: false
      });
    }
  },

  // 处理出生信息保存事件
  onBirthInfoSave: function(e) {
    const birthInfo = e.detail;
    this.setData({ birthInfo });
    this.calculateFengshui(birthInfo);
  },

  // 重新计算
  recalculate: function() {
    if (this.data.birthInfo) {
      this.calculateFengshui(this.data.birthInfo);
    }
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({
          floorPlan: res.tempFilePaths[0],
          layoutResult: null,
          error: null
        })
        // 选择图片后自动开始分析
        this.analyzeLayout()
      }
    })
  },

  // 预览图片
  previewImage() {
    if (this.data.floorPlan) {
      wx.previewImage({
        urls: [this.data.floorPlan]
      })
    }
  },

  // 分析户型布局
  analyzeLayout() {
    if (!this.data.floorPlan) {
      this.setData({
        error: '请先上传户型图'
      })
      return
    }

    this.setData({
      loading: true,
      error: null
    })

    // 模拟分析过程
    setTimeout(() => {
      // 生成分析结果
      const layoutResult = {
        baguaAnalysis: [
          { position: '坎位（北）', analysis: '属水，主智慧、事业，宜放置书桌、办公用品' },
          { position: '艮位（东北）', analysis: '属土，主稳定、积累，宜放置储物柜、保险箱' },
          { position: '震位（东）', analysis: '属木，主生机、发展，宜放置植物、绿色装饰' },
          { position: '巽位（东南）', analysis: '属木，主财运、人缘，宜放置招财物品' },
          { position: '离位（南）', analysis: '属火，主名声、地位，宜放置红色装饰、照明' },
          { position: '坤位（西南）', analysis: '属土，主家庭、健康，宜放置沙发、床铺' },
          { position: '兑位（西）', analysis: '属金，主喜悦、收获，宜放置金属装饰' },
          { position: '乾位（西北）', analysis: '属金，主权威、领导，宜放置办公桌、座椅' }
        ],
        furnitureAdvice: [
          { room: '客厅', advice: '沙发宜靠实墙，茶几选择圆形或椭圆形，避免尖角' },
          { room: '卧室', advice: '床头宜靠实墙，避免正对门窗，床尾不宜正对镜子' },
          { room: '厨房', advice: '灶台宜靠墙，避免正对门，水槽与灶台不宜相对' },
          { room: '书房', advice: '书桌宜靠窗，座位背后宜有靠山，避免背对门' }
        ],
        colorAdvice: [
          { area: '客厅', colors: '米色、浅灰、淡蓝', colorCode: '#F5F5F5' },
          { area: '卧室', colors: '淡粉、浅紫、米白', colorCode: '#FFF0F5' },
          { area: '厨房', colors: '白色、浅绿、米黄', colorCode: '#F0FFF0' },
          { area: '书房', colors: '浅蓝、米白、淡绿', colorCode: '#F0F8FF' }
        ],
        optimizationAdvice: [
          '大门入口处宜设置玄关，避免直冲',
          '客厅与餐厅之间可用屏风或植物分隔',
          '卧室门不宜正对卫生间门',
          '厨房门不宜正对卧室门',
          '阳台可种植绿色植物，增加生气',
          '客厅可摆放水晶、铜钱等招财物品',
          '卧室床头可放置紫水晶，助眠安神',
          '书房可摆放文昌塔，提升学业运'
        ]
      }

      this.setData({
        layoutResult,
        loading: false
      })
    }, 1500) // 延迟1.5秒模拟分析过程
  },
})